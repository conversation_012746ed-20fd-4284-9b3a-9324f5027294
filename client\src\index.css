@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 221.2 83.2% 53.3%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: var(--primary);
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Enhanced Work Order Carousel Theme Variables */
    --carousel-accent: 221.2 83.2% 53.3%;
    --carousel-border-color: 0 0% 100%;
    --carousel-glow-color: 221.2 83.2% 53.3%;
    --carousel-navy-bg: 200 60% 8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 91.2% 59.8%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: var(--primary);
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 94.1%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Enhanced Dark Mode Carousel Variables */
    --carousel-accent: 217.2 91.2% 59.8%;
    --carousel-border-color: 0 0% 100%;
    --carousel-glow-color: 217.2 91.2% 59.8%;
    --carousel-navy-bg: 200 60% 8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: "Inter", sans-serif;
  }
}

@layer components {

  /* Glass morphism effect */
  .glass-card {
    @apply backdrop-blur-xl bg-background/80 border border-border/50 shadow-lg;
  }

  /* Mobile optimizations */
  .mobile-optimized {
    @apply touch-manipulation;
  }

  /* Hover effects */
  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105 active:scale-95;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent;
  }

  /* Sidebar specific styles */
  .sidebar-link {
    @apply flex items-center gap-3 px-3 py-2 text-sm font-medium text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground rounded-md transition-colors duration-200;
  }

  .sidebar-link.active {
    @apply bg-sidebar-primary text-sidebar-primary-foreground;
  }

  .sidebar-section {
    @apply space-y-1;
  }

  /* Pulse dot animation */
  .pulse-dot {
    animation: pulse-dot 2s infinite;
  }

  @keyframes pulse-dot {

    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.5;
    }
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Enhanced Work Order Carousel Styles */
  .carousel-card {
    background: #04101E;
    border: 2px solid hsl(var(--carousel-border-color));
    border-radius: 20px;
    box-shadow: 0 0 8px hsl(var(--carousel-glow-color) / 0.3);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .carousel-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
    background-size: 20px 20px, 25px 25px;
    pointer-events: none;
    opacity: 0.5;
  }

  .carousel-card:hover {
    border-width: 4px;
    box-shadow: 0 0 8px hsl(var(--carousel-glow-color) / 1);
  }

  .carousel-card-content {
    position: relative;
    z-index: 1;
    padding: 1.5rem;
  }

  /* Fluid Typography */
  .carousel-title {
    font-size: clamp(1.125rem, 2.5vw, 1.375rem);
    line-height: 1.2;
    font-weight: 700;
    color: white;
  }

  .carousel-description {
    font-size: clamp(0.875rem, 2vw, 1rem);
    line-height: 1.4;
    color: rgba(255, 255, 255, 0.7);
  }

  .carousel-meta {
    font-size: clamp(0.75rem, 1.5vw, 0.875rem);
    color: rgba(255, 255, 255, 0.6);
  }

  /* Scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

@layer utilities {

  /* Animation classes */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }

    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes scaleIn {
    from {
      transform: scale(0.95);
      opacity: 0;
    }

    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Touch optimizations for mobile */
  .touch-optimized {
    @apply touch-manipulation select-none;
  }

  /* Focus improvements for accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }
}

/* Custom scrollbar for webkit browsers */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
}

/* Responsive text sizes */
@media (max-width: 640px) {
  .text-3xl {
    @apply text-2xl;
  }

  .text-2xl {
    @apply text-xl;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-card {
    @apply bg-background border-2;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .hover-scale {
    @apply transform-none;
  }

  .animate-fade-in,
  .animate-slide-up,
  .animate-scale-in {
    animation: none;
  }

  /* Disable 3D rotations for carousel */
  .carousel-card {
    transform: none !important;
  }

  /* Keep simple fade transitions only */
  .carousel-card-reduced-motion {
    transition: opacity 0.3s ease-in-out;
  }
}