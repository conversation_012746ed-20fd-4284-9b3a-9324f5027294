import React, { useState, useEffect, useCallback, useMemo, memo, lazy, Suspense } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { format, getMonth, getYear, setMonth, setYear, addDays, addWeeks, addMonths, differenceInDays } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ThemedInput } from '@/components/ui/themed-input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { FailureAnalysisEngine, VibrationData, FailureAnalysis } from '@/utils/failureAnalysisEngine';
import { FailureAnalysisCard, MasterHealthDashboard, SeverityIndicator } from '@/components/analytics/AnalyticsComponents';
import { EnhancedFailureAnalysisCarousel } from '@/components/analytics/EnhancedFailureAnalysisCarousel';
import { ReliabilityEngineService } from '@/services/reliabilityEngineService';
import {
    CalendarIcon,
    Activity,
    Thermometer,
    Zap,
    Settings,
    TrendingUp,
    AlertTriangle,
    CheckCircle,
    XCircle,
    Info,
    Plus,
    ChevronRight,
    ChevronLeft,
    ArrowLeft,
    MapPin,
    Gauge,
    Waves,
    Factory,
    Cog,
    Shield,
    Target,
    Cpu,
    Database,
    Search,
    Filter,
    Eye,
    Download,
    Upload,
    FileText,
    Edit,
    Brain,
    Lightbulb,
    TrendingDown,
    Clock,
    AlertCircle,
    RefreshCw,
    X,
    CheckCircle2,
    Wrench,
    ClipboardList,
    Timer,
    Zap as Lightning,
    TrendingUp as TrendUp,
    BarChart,
    PieChart,
    LineChart,
    Sparkles,
    Star,
    Award,
    Layers,
    Grid,
    Maximize2,
    Minimize2,
    MoreVertical,
    PlayCircle,
    DollarSign
} from 'lucide-react';
import { useAssetContext } from '@/contexts/AssetContext';
import { useToast } from '@/hooks/use-toast';
import { useThemeColors } from '@/hooks/use-theme-colors';
import { useTheme } from '@/hooks/use-theme';
import { EnhancedChart } from '@/components/charts/EnhancedChart';
import type { VibrationHistoryRecord } from '@/data/vibrationHistoryData';
import { allHierarchicalEquipment, zoneA } from '@/data/hierarchicalAssetData';
import { MultiEquipmentSelector } from '@/components/maintenance/MultiEquipmentSelector';
import { MultiEquipmentSpecifications } from '@/components/maintenance/MultiEquipmentSpecifications';
import { cn } from '@/lib/utils';
import { getVibrationInputColor, getVibrationTooltip, analyzeVibrationData, ISO10816_THRESHOLDS, getISO10816Zone, calcRMSVelocity } from '@/utils/vibrationUtils';
// Removed AI Assessment Center imports

interface EnhancedVibrationFormProps {
    open: boolean;
    onClose: () => void;
    record?: VibrationHistoryRecord | null;
    readOnly?: boolean;
}

// Intelligent Maintenance Planning Interfaces
interface MaintenanceTask {
    id: string;
    title: string;
    description: string;
    priority: 'critical' | 'urgent' | 'high' | 'medium' | 'low';
    estimatedDuration: number; // hours
    scheduledDate: Date;
    dueDate: Date;
    status: 'pending' | 'in-progress' | 'completed' | 'overdue';
    assignedTo?: string;
    equipmentId: string;
    failureMode?: string;
    rulDays?: number;
    workOrderNumber?: string;
    completionNotes?: string;
    effectiveness?: number; // 0-100%
}

interface MaintenancePlan {
    id: string;
    equipmentId: string;
    planName: string;
    createdDate: Date;
    lastUpdated: Date;
    tasks: MaintenanceTask[];
    totalEstimatedHours: number;
    completionPercentage: number;
    nextCriticalTask?: MaintenanceTask;
    rulPrediction?: {
        estimatedRUL: number;
        confidence: number;
        criticalFailureDate: Date;
    };
}

interface ChartDataPoint {
    x: number | string | Date;
    y: number;
    label?: string;
    color?: string;
}

interface AdvancedChartConfig {
    type: 'line' | 'bar' | 'scatter' | 'doughnut' | 'radar';
    title: string;
    data: ChartDataPoint[];
    options?: any;
    height?: number;
}

// Multi-step wizard steps
const FORM_STEPS = [
    { id: 'equipment', title: 'Equipment Selection', icon: Factory, description: 'Select equipment for vibration monitoring' },
    { id: 'operational', title: 'Operational Data', icon: Gauge, description: 'Enter operational parameters' },
    { id: 'vibration', title: 'Vibration Measurements', icon: Waves, description: 'Record vibration readings' },
    { id: 'analysis', title: 'Analysis & Review', icon: BarChart, description: 'Review and analyze data' }
];

// Using standardized vibration utilities imported from vibrationUtils.ts

// Equipment categories with enhanced metadata
const EQUIPMENT_CATEGORIES = {
    pump: {
        label: 'Pumps',
        icon: Cpu,
        color: 'bg-blue-500',
        description: 'Centrifugal and positive displacement pumps',
        vibrationLimits: { good: 2.8, acceptable: 7.1, unacceptable: 18.0 }
    },
    motor: {
        label: 'Motors',
        icon: Zap,
        color: 'bg-green-500',
        description: 'Electric motors and drives',
        vibrationLimits: { good: 1.8, acceptable: 4.5, unacceptable: 11.0 }
    }
};

// Generate enhanced equipment options with memory optimization
const generateEquipmentOptions = () => {
    return allHierarchicalEquipment
        .filter(eq => ['pump', 'motor'].includes(eq.category))
        .slice(0, 30) // Limit to first 30 items for memory optimization
        .map(equipment => ({
            id: equipment.id,
            name: equipment.name,
            manufacturer: equipment.manufacturer,
            model: equipment.model,
            assetTag: equipment.assetTag,
            category: equipment.category,
            location: equipment.location,
            serialNumber: equipment.serialNumber,
            specifications: equipment.specifications,
            hierarchicalPath: `${equipment.location?.zone || 'Zone A'} → ${equipment.location?.station || 'Unknown'} → ${equipment.location?.line || equipment.location?.system || 'Equipment'}`,
            categoryInfo: EQUIPMENT_CATEGORIES[equipment.category as keyof typeof EQUIPMENT_CATEGORIES]
        }));
};

const equipmentOptions = generateEquipmentOptions();

// Enhanced form structure with comprehensive data
const initialFormData = {
    // Step 1: Equipment Selection
    selectedEquipment: '',
    equipmentDetails: null as any,
    selectedEquipmentList: [] as string[],

    // Step 2: Operational Data
    date: format(new Date(), 'yyyy-MM-dd'),
    time: format(new Date(), 'HH:mm'),
    operator: '',
    shift: '',

    // Operating Conditions
    operatingHours: '',
    operatingPower: '',
    operatingSpeed: '',
    operatingTemperature: '',
    operatingPressure: '',
    operatingFlow: '',
    operatingVoltage: '',
    operatingCurrent: '',
    operatingFrequency: '',
    operatingEfficiency: '',

    // Environmental Conditions
    ambientTemperature: '',
    humidity: '',
    vibrationLevel: '',
    noiseLevel: '',

    // Process Parameters
    suctionPressure: '',
    dischargePressure: '',
    flowRate: '',
    head: '',
    powerConsumption: '',
    efficiency: '',

    // Step 3: Vibration Measurements - Enhanced with Legs 1-4 for Pump and Motor
    vibrationData: {
        // Pump Measurements - Using standardized field names with NDE, DE, and Legs 1-4
        pump: {
            nde: {
                bv: '',      // Bearing vibration
                bg: '',      // Bearing gap
                accV: '',    // Acceleration Vertical
                accH: '',    // Acceleration Horizontal
                accAxl: '',  // Acceleration Axial
                velV: '',    // Velocity Vertical
                velH: '',    // Velocity Horizontal
                velAxl: '',  // Velocity Axial
                temp: ''     // Temperature
            },
            de: {
                bv: '',      // Bearing vibration
                bg: '',      // Bearing gap
                accV: '',    // Acceleration Vertical
                accH: '',    // Acceleration Horizontal
                accAxl: '',  // Acceleration Axial
                velV: '',    // Velocity Vertical
                velH: '',    // Velocity Horizontal
                velAxl: '',  // Velocity Axial
                temp: ''     // Temperature
            },
            leg1: {
                velocity: ''  // Single velocity reading (mm/s)
            },
            leg2: {
                velocity: ''  // Single velocity reading (mm/s)
            },
            leg3: {
                velocity: ''  // Single velocity reading (mm/s)
            },
            leg4: {
                velocity: ''  // Single velocity reading (mm/s)
            }
        },
        // Motor Measurements - Using standardized field names with NDE, DE, and Legs 1-4
        motor: {
            nde: {
                bv: '',      // Bearing vibration
                bg: '',      // Bearing gap
                accV: '',    // Acceleration Vertical
                accH: '',    // Acceleration Horizontal
                accAxl: '',  // Acceleration Axial
                velV: '',    // Velocity Vertical
                velH: '',    // Velocity Horizontal
                velAxl: '',  // Velocity Axial
                temp: ''     // Temperature
            },
            de: {
                bv: '',      // Bearing vibration
                bg: '',      // Bearing gap
                accV: '',    // Acceleration Vertical
                accH: '',    // Acceleration Horizontal
                accAxl: '',  // Acceleration Axial
                velV: '',    // Velocity Vertical
                velH: '',    // Velocity Horizontal
                velAxl: '',  // Velocity Axial
                temp: ''     // Temperature
            },
            leg1: {
                velocity: ''  // Single velocity reading (mm/s)
            },
            leg2: {
                velocity: ''  // Single velocity reading (mm/s)
            },
            leg3: {
                velocity: ''  // Single velocity reading (mm/s)
            },
            leg4: {
                velocity: ''  // Single velocity reading (mm/s)
            }
        }
    },

    // Step 4: Analysis & Review
    overallCondition: '',
    recommendations: '',
    nextInspectionDate: '',
    notes: '',
    priority: '',
    maintenanceRequired: false,
    immediateAction: false
};

// AI Assessment interfaces
interface AIConditionAssessment {
    healthScore: number;
    overallCondition: string;
    priority: string;
    confidence: number;
    maintenanceRequired: boolean;
    immediateAction: boolean;
    nextInspectionDate: string;
    recommendations: Array<{
        title: string;
        description: string;
    }>;
    anomalies: any[];
}

// Utility function for safe display of values
const safeDisplayUtil = (value: any, decimals: number = 0, fallback: string = 'N/A'): string => {
    if (value === null || value === undefined) {
        return fallback;
    }

    if (typeof value === 'string' && value.trim() === '') {
        return fallback;
    }

    const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);

    if (isNaN(numValue) || !isFinite(numValue)) {
        return fallback;
    }

    return numValue.toFixed(decimals);
};

// Removed AI Assessment Engine







const EnhancedVibrationForm: React.FC<EnhancedVibrationFormProps> = ({
    open,
    onClose,
    record,
    readOnly = false
}) => {
    const { addVibrationHistoryEntry } = useAssetContext();
    const { toast } = useToast();
    const { getThemeClasses } = useThemeColors();
    const { theme } = useTheme();
    const themeClasses = getThemeClasses();

    // Form state
    const [currentStep, setCurrentStep] = useState(0);
    const [formProgress, setFormProgress] = useState(0);
    const [selectedEquipment, setSelectedEquipment] = useState<string[]>([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [activePoint, setActivePoint] = useState<string | null>(null);
    const [showAlert, setShowAlert] = useState(false);
    const [alertType, setAlertType] = useState<'success' | 'error'>('success');
    const [alertDetails, setAlertDetails] = useState<any>({});
    const [showAddEquipment, setShowAddEquipment] = useState(false);
    const [addMode, setAddMode] = useState(false);

    // Add Equipment form state
    const [newEquipment, setNewEquipment] = useState({
        name: '',
        type: '',
        category: '',
        manufacturer: '',
        model: '',
        serialNumber: '',
        location: '',
        status: 'operational',
        condition: 'good',
    });

    // Intelligent Maintenance Planning State
    const [maintenancePlan, setMaintenancePlan] = useState<MaintenancePlan | null>(null);
    const [selectedTask, setSelectedTask] = useState<MaintenanceTask | null>(null);
    const [showMaintenancePlanner, setShowMaintenancePlanner] = useState(false);
    const [taskFilter, setTaskFilter] = useState<'all' | 'pending' | 'in-progress' | 'completed'>('all');

    // Advanced Charts State
    const [chartConfigs, setChartConfigs] = useState<AdvancedChartConfig[]>([]);
    const [selectedChartType, setSelectedChartType] = useState<'trend' | 'statistical' | 'rul' | 'health'>('trend');
    const [chartTimeRange, setChartTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
    const [showChartsPanel, setShowChartsPanel] = useState(false);

    // Real-time Data Visualization State
    const [realTimeData, setRealTimeData] = useState<any>({});
    const [isLiveDataActive, setIsLiveDataActive] = useState(false);
    const [dataUpdateInterval, setDataUpdateInterval] = useState<NodeJS.Timeout | null>(null);
    const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());
    const [liveDataBuffer, setLiveDataBuffer] = useState<any[]>([]);
    const [anomalyDetectionActive, setAnomalyDetectionActive] = useState(true);

    // AI Assessment state
    const [aiAssessment, setAiAssessment] = useState<any>(null);
    const [isAssessing, setIsAssessing] = useState(false);
    const [lastAssessmentTime, setLastAssessmentTime] = useState<Date | null>(null);

    // Failure Analysis Cards state
    const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());

    // Reliability enhancement state
    const [reliabilityData, setReliabilityData] = useState<any>(null);

    // Enhanced user feedback state
    const [userFeedback, setUserFeedback] = useState<{
        type: 'error' | 'warning' | 'info' | 'success';
        title: string;
        message: string;
        show: boolean;
    }>({
        type: 'info',
        title: '',
        message: '',
        show: false
    });

    // Safe display function with user feedback
    const safeDisplay = (value: any, decimals: number = 0, fallback: string = 'N/A', context: string = ''): string => {
        if (value === null || value === undefined) {
            if (context) {
                setUserFeedback({
                    type: 'warning',
                    title: 'Missing Data',
                    message: `${context} data is missing. Please check your input values.`,
                    show: true
                });
            }
            return fallback;
        }

        const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);

        if (isNaN(numValue) || !isFinite(numValue)) {
            setUserFeedback({
                type: 'error',
                title: 'Calculation Error',
                message: `Unable to calculate ${context || 'value'}. This may be due to invalid input data or a calculation error. Please verify your vibration readings and try again.`,
                show: true
            });
            return fallback;
        }

        return decimals > 0 ? numValue.toFixed(decimals) : Math.round(numValue).toString();
    };

    // Enhanced error handling for calculations
    const handleCalculationError = (error: any, context: string) => {
        console.error(`Calculation error in ${context}:`, error);
        setUserFeedback({
            type: 'error',
            title: 'Calculation Error',
            message: `An error occurred while calculating ${context}. Please check your input values and try again. If the problem persists, please contact support.`,
            show: true
        });
    };

    // Auto-dismiss success messages after 5 seconds
    useEffect(() => {
        if (userFeedback.show && userFeedback.type === 'success') {
            const timer = setTimeout(() => {
                setUserFeedback(prev => ({ ...prev, show: false }));
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [userFeedback.show, userFeedback.type]);

    // Toggle card expansion
    const toggleCardExpansion = (cardType: string) => {
        setExpandedCards(prev => {
            const newSet = new Set(prev);
            if (newSet.has(cardType)) {
                newSet.delete(cardType);
            } else {
                newSet.add(cardType);
            }
            return newSet;
        });
    };



    // Calculate real reliability analysis based on actual entered vibration data
    const calculateRealReliabilityAnalysis = (formData: any) => {
        try {
            // Validate that we have meaningful vibration data before proceeding
            if (!hasValidVibrationData(formData.vibrationData)) {
                setUserFeedback({
                    type: 'warning',
                    title: 'Insufficient Data',
                    message: 'Reliability analysis requires vibration measurements. Please enter vibration data first.',
                    show: true
                });
                return null; // Return null if no valid vibration data
            }

            // Extract actual vibration measurements from form
            const vibrationData = formData.vibrationData || {};

            // Calculate average vibration levels from actual entered data
            const pumpVibrations = [];
            const motorVibrations = [];

            // Pump NDE/DE measurements
            if (vibrationData.pump?.nde) {
                const nde = vibrationData.pump.nde;
                pumpVibrations.push(
                    parseFloat(nde.velH || '0'),
                    parseFloat(nde.velV || '0'),
                    parseFloat(nde.velAxl || '0')
                );
            }
            if (vibrationData.pump?.de) {
                const de = vibrationData.pump.de;
                pumpVibrations.push(
                    parseFloat(de.velH || '0'),
                    parseFloat(de.velV || '0'),
                    parseFloat(de.velAxl || '0')
                );
            }

            // Pump leg measurements
            ['leg1', 'leg2', 'leg3', 'leg4'].forEach(leg => {
                if (vibrationData.pump?.[leg]?.velocity) {
                    pumpVibrations.push(parseFloat(vibrationData.pump[leg].velocity));
                }
            });

            // Motor NDE/DE measurements
            if (vibrationData.motor?.nde) {
                const nde = vibrationData.motor.nde;
                motorVibrations.push(
                    parseFloat(nde.velH || '0'),
                    parseFloat(nde.velV || '0'),
                    parseFloat(nde.velAxl || '0')
                );
            }
            if (vibrationData.motor?.de) {
                const de = vibrationData.motor.de;
                motorVibrations.push(
                    parseFloat(de.velH || '0'),
                    parseFloat(de.velV || '0'),
                    parseFloat(de.velAxl || '0')
                );
            }

            // Motor leg measurements
            ['leg1', 'leg2', 'leg3', 'leg4'].forEach(leg => {
                if (vibrationData.motor?.[leg]?.velocity) {
                    motorVibrations.push(parseFloat(vibrationData.motor[leg].velocity));
                }
            });

            // Calculate averages (filter out zeros and invalid values)
            const pumpNonZero = pumpVibrations.filter(v => !isNaN(v) && isFinite(v) && v > 0);
            const motorNonZero = motorVibrations.filter(v => !isNaN(v) && isFinite(v) && v > 0);

            const pumpAvg = pumpNonZero.length > 0 ? pumpNonZero.reduce((a, b) => a + b, 0) / pumpNonZero.length : 0;
            const motorAvg = motorNonZero.length > 0 ? motorNonZero.reduce((a, b) => a + b, 0) / motorNonZero.length : 0;

            // Calculate system average more safely
            let systemAvg = 0;
            if (pumpAvg > 0 && motorAvg > 0) {
                systemAvg = (pumpAvg + motorAvg) / 2;
            } else if (pumpAvg > 0) {
                systemAvg = pumpAvg;
            } else if (motorAvg > 0) {
                systemAvg = motorAvg;
            } else {
                systemAvg = 1.0; // Default safe value when no data
            }

            // Ensure systemAvg is valid
            systemAvg = isNaN(systemAvg) || !isFinite(systemAvg) ? 1.0 : Math.max(0.1, systemAvg);



            // Get operational parameters from actual form data with safe parsing
            const safeParseFloat = (value: any, defaultValue: number): number => {
                if (value === null || value === undefined || value === '') return defaultValue;
                const parsed = typeof value === 'string' ? parseFloat(value) : Number(value);
                return isNaN(parsed) || !isFinite(parsed) ? defaultValue : Math.max(0, parsed);
            };

            const operatingHours = safeParseFloat(formData.operatingHours, 4000);
            const operatingPower = safeParseFloat(formData.operatingPower, 75);
            const efficiency = safeParseFloat(formData.efficiency, 85);
            const temperature = safeParseFloat(formData.ambientTemperature, 25);

            // Calculate reliability metrics based on actual vibration levels
            // Higher vibration = lower reliability
            const baseMTBF = 6000; // Base MTBF in hours
            const vibrationFactor = Math.max(0.1, Math.min(1.0, 1.0 - (systemAvg - 2.0) / 10.0)); // Reduce MTBF with higher vibration
            const temperatureFactor = Math.max(0.8, Math.min(1.0, 1.0 - (temperature - 25) / 100)); // Temperature effect
            const powerFactor = Math.max(0.9, Math.min(1.2, operatingPower / 100)); // Power efficiency effect

            const mtbf = Math.max(100, baseMTBF * vibrationFactor * temperatureFactor * powerFactor); // Ensure minimum MTBF
            const mttr = 24; // Mean time to repair
            const availability = Math.max(0, Math.min(100, (mtbf / (mtbf + mttr)) * 100)); // Ensure valid percentage
            const failureRate = mtbf > 0 ? 1 / mtbf : 0; // Prevent division by zero

            // Weibull parameters based on actual conditions
            const beta = Math.max(1.0, Math.min(3.0, systemAvg > 6 ? 1.2 : systemAvg > 3 ? 2.0 : 2.5)); // Shape parameter
            const eta = Math.max(100, mtbf * Math.pow(Math.log(2), 1 / beta)); // Scale parameter

            // Calculate reliability at current operating time
            const reliabilityAtTime = Math.max(0, Math.min(1, Math.exp(-Math.pow(operatingHours / eta, beta))));

            // Calculate Remaining Useful Life
            const targetReliability = 0.1; // 10% reliability threshold
            const logTarget = Math.log(targetReliability);
            const rulTime = eta * Math.pow(-logTarget, 1 / beta);
            const rul = Math.max(0, isNaN(rulTime) || !isFinite(rulTime) ? 1000 : rulTime - operatingHours);

            // Generate failure modes based on actual vibration levels
            const failureModes = [
                {
                    mode: "Bearing Failure",
                    probability: Math.min(0.8, 0.05 + (systemAvg / 10)),
                    severity: 8,
                    detectability: 6,
                    rpn: Math.round((0.05 + (systemAvg / 10)) * 8 * 6 * 100)
                },
                {
                    mode: "Unbalance",
                    probability: Math.min(0.6, 0.03 + (systemAvg / 15)),
                    severity: 6,
                    detectability: 4,
                    rpn: Math.round((0.03 + (systemAvg / 15)) * 6 * 4 * 100)
                },
                {
                    mode: "Misalignment",
                    probability: Math.min(0.5, 0.02 + (systemAvg / 20)),
                    severity: 7,
                    detectability: 5,
                    rpn: Math.round((0.02 + (systemAvg / 20)) * 7 * 5 * 100)
                },
                {
                    mode: "Looseness",
                    probability: Math.min(0.4, 0.01 + (systemAvg / 25)),
                    severity: 5,
                    detectability: 3,
                    rpn: Math.round((0.01 + (systemAvg / 25)) * 5 * 3 * 100)
                }
            ];

            // Maintenance optimization
            const optimalInterval = Math.max(100, mtbf * 0.7); // 70% of MTBF, minimum 100 hours
            const failureCost = 25000;
            const maintenanceCost = 3000;
            const failureCostPerYear = mtbf > 0 ? (8760 / mtbf * failureCost) : 0;
            const maintenanceCostPerYear = optimalInterval > 0 ? (8760 / optimalInterval * maintenanceCost) : 0;
            const costSavings = Math.max(0, failureCostPerYear - maintenanceCostPerYear);

            // Overall health based on actual vibration levels
            const healthScore = Math.max(0, Math.min(100, 100 - Math.max(0, systemAvg - 1.0) * 12));

            // Trend direction based on vibration severity
            let trendDirection = "improving";
            if (systemAvg > 6.0) trendDirection = "degrading";
            else if (systemAvg > 3.0) trendDirection = "stable";

            // Helper function to ensure valid numbers in results
            const safeRound = (value: number, decimals: number = 0): number => {
                if (isNaN(value) || !isFinite(value)) return 0;
                const factor = Math.pow(10, decimals);
                return Math.round(value * factor) / factor;
            };

            const result = {
                reliability_metrics: {
                    mtbf: safeRound(mtbf),
                    mttr: safeRound(mttr),
                    availability: safeRound(availability, 2),
                    reliability_at_time: safeRound(reliabilityAtTime, 4),
                    failure_rate: safeRound(failureRate, 6)
                },
                weibull_analysis: {
                    beta: safeRound(beta, 2),
                    eta: safeRound(eta),
                    gamma: 0,
                    r_squared: 0.92
                },
                rul_prediction: {
                    remaining_useful_life: safeRound(rul),
                    confidence_interval: {
                        lower: safeRound(rul * 0.8),
                        upper: safeRound(rul * 1.2)
                    },
                    prediction_accuracy: 85
                },
                failure_modes: failureModes,
                maintenance_optimization: {
                    optimal_interval: safeRound(optimalInterval),
                    cost_savings: safeRound(costSavings),
                    recommended_actions: [
                        systemAvg > 5 ? "Immediate inspection required" : "Schedule preventive maintenance",
                        "Monitor vibration trends closely",
                        systemAvg > 4 ? "Check bearing condition" : "Check lubrication levels",
                        systemAvg > 6 ? "Consider equipment replacement" : "Verify alignment",
                        "Review operating parameters"
                    ]
                },
                condition_indicators: {
                    overall_health: safeRound(healthScore, 1),
                    degradation_rate: safeRound(systemAvg / 100, 4),
                    anomaly_score: Math.round(Math.min(100, systemAvg * 15) * 10) / 10,
                    trend_direction: trendDirection
                }
            };


            return result;
        } catch (error) {
            console.error('Error in reliability calculation:', error);
            handleCalculationError(error, 'reliability analysis');
            return null;
        }
    };

    // Removed AI Assessment Engine

    // Initialize Reliability Engine Service
    const reliabilityService = ReliabilityEngineService.getInstance();

    // Form setup
    const methods = useForm({
        defaultValues: initialFormData
    });

    const { control, handleSubmit, watch, setValue, getValues, formState: { errors } } = methods;
    const formValues = watch();

    // Get first equipment for reference
    const firstEquipment = selectedEquipment.length > 0
        ? equipmentOptions.find(eq => eq.id === selectedEquipment[0])
        : null;

    // Calculate form progress
    useEffect(() => {
        const totalSteps = FORM_STEPS.length;
        const progress = ((currentStep + 1) / totalSteps) * 100;
        setFormProgress(progress);
    }, [currentStep]);

    // Helper function to validate if vibration data has meaningful values
    const hasValidVibrationData = (vibrationData: any): boolean => {
        if (!vibrationData) return false;

        // Check pump measurements for non-zero values
        const pumpValues = [];
        if (vibrationData.pump?.nde) {
            pumpValues.push(
                parseFloat(vibrationData.pump.nde.velV || '0'),
                parseFloat(vibrationData.pump.nde.velH || '0'),
                parseFloat(vibrationData.pump.nde.velAxl || '0')
            );
        }
        if (vibrationData.pump?.de) {
            pumpValues.push(
                parseFloat(vibrationData.pump.de.velV || '0'),
                parseFloat(vibrationData.pump.de.velH || '0'),
                parseFloat(vibrationData.pump.de.velAxl || '0')
            );
        }
        // Check pump leg measurements
        ['leg1', 'leg2', 'leg3', 'leg4'].forEach(leg => {
            if (vibrationData.pump?.[leg]?.velocity) {
                pumpValues.push(parseFloat(vibrationData.pump[leg].velocity || '0'));
            }
        });

        // Check motor measurements for non-zero values
        const motorValues = [];
        if (vibrationData.motor?.nde) {
            motorValues.push(
                parseFloat(vibrationData.motor.nde.velV || '0'),
                parseFloat(vibrationData.motor.nde.velH || '0'),
                parseFloat(vibrationData.motor.nde.velAxl || '0')
            );
        }
        if (vibrationData.motor?.de) {
            motorValues.push(
                parseFloat(vibrationData.motor.de.velV || '0'),
                parseFloat(vibrationData.motor.de.velH || '0'),
                parseFloat(vibrationData.motor.de.velAxl || '0')
            );
        }
        // Check motor leg measurements
        ['leg1', 'leg2', 'leg3', 'leg4'].forEach(leg => {
            if (vibrationData.motor?.[leg]?.velocity) {
                motorValues.push(parseFloat(vibrationData.motor[leg].velocity || '0'));
            }
        });

        // Return true only if we have at least one non-zero measurement
        const allValues = [...pumpValues, ...motorValues];
        return allValues.some(value => value > 0);
    };

    // Simple AI Assessment function
    const performAIAssessment = useCallback(() => {
        if (isAssessing) return;

        setIsAssessing(true);

        try {
            const vibrationData = formValues.vibrationData;
            if (!hasValidVibrationData(vibrationData)) {
                setAiAssessment(null);
                return;
            }

            // Simple assessment based on vibration values
            const allValues = [];

            // Collect all vibration values
            if (vibrationData.pump?.nde) {
                allValues.push(
                    parseFloat(vibrationData.pump.nde.velV || '0'),
                    parseFloat(vibrationData.pump.nde.velH || '0'),
                    parseFloat(vibrationData.pump.nde.velAxl || '0')
                );
            }
            if (vibrationData.pump?.de) {
                allValues.push(
                    parseFloat(vibrationData.pump.de.velV || '0'),
                    parseFloat(vibrationData.pump.de.velH || '0'),
                    parseFloat(vibrationData.pump.de.velAxl || '0')
                );
            }
            if (vibrationData.motor?.nde) {
                allValues.push(
                    parseFloat(vibrationData.motor.nde.velV || '0'),
                    parseFloat(vibrationData.motor.nde.velH || '0'),
                    parseFloat(vibrationData.motor.nde.velAxl || '0')
                );
            }
            if (vibrationData.motor?.de) {
                allValues.push(
                    parseFloat(vibrationData.motor.de.velV || '0'),
                    parseFloat(vibrationData.motor.de.velH || '0'),
                    parseFloat(vibrationData.motor.de.velAxl || '0')
                );
            }

            const maxValue = Math.max(...allValues.filter(v => v > 0));
            const avgValue = allValues.filter(v => v > 0).reduce((a, b) => a + b, 0) / allValues.filter(v => v > 0).length;

            // Simple health scoring
            let healthScore = 100;
            let overallCondition = 'good';
            let priority = 'low';

            if (maxValue > 10) {
                healthScore = 30;
                overallCondition = 'critical';
                priority = 'critical';
            } else if (maxValue > 7) {
                healthScore = 50;
                overallCondition = 'poor';
                priority = 'high';
            } else if (maxValue > 4) {
                healthScore = 70;
                overallCondition = 'fair';
                priority = 'medium';
            } else if (maxValue > 2) {
                healthScore = 85;
                overallCondition = 'good';
                priority = 'low';
            }

            const assessment = {
                healthScore,
                overallCondition,
                priority,
                confidence: Math.min(95, 70 + (allValues.filter(v => v > 0).length * 5)),
                maintenanceRequired: healthScore < 70,
                immediateAction: healthScore < 50,
                nextInspectionDate: new Date(Date.now() + (healthScore > 80 ? 90 : healthScore > 60 ? 30 : 7) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                recommendations: [
                    {
                        title: 'Vibration Analysis',
                        description: `Maximum vibration level: ${maxValue.toFixed(2)} mm/s. ${overallCondition === 'good' ? 'Continue normal operation.' : 'Consider maintenance action.'}`
                    }
                ],
                anomalies: []
            };

            setAiAssessment(assessment);
            setLastAssessmentTime(new Date());
        } catch (error) {
            console.error('Error performing AI assessment:', error);
            setAiAssessment(null);
        } finally {
            setIsAssessing(false);
        }
    }, [formValues.vibrationData, isAssessing]);

    // Enhanced real-time vibration data synchronization with automatic redirect
    useEffect(() => {
        const hasVibrationData = hasValidVibrationData(formValues.vibrationData);

        if (hasVibrationData && selectedEquipment.length > 0) {
            // Trigger AI assessment with debouncing to prevent excessive calls
            const timeoutId = setTimeout(() => {
                performAIAssessment();

                // Automatic redirect to Step 4 (Analysis & Review) when vibration data is entered
                // Only redirect if we're currently on Step 3 (Vibration Measurements)
                if (currentStep === 2) {
                    setCurrentStep(3); // Redirect to Analysis & Review step
                }
            }, 500);

            return () => clearTimeout(timeoutId);
        } else {
            // Clear AI assessment when no valid data
            setAiAssessment(null);
        }
    }, [formValues.vibrationData, formValues.operatingHours, formValues.operatingPower, formValues.efficiency, selectedEquipment, currentStep, performAIAssessment]);

    // Enhanced reliability data synchronization with automatic calculations
    useEffect(() => {
        const hasVibrationData = hasValidVibrationData(formValues.vibrationData);

        if (hasVibrationData && selectedEquipment.length > 0) {
            // Reduced debounce time for faster automatic calculations
            const timeoutId = setTimeout(async () => {
                try {
                    // Try Python backend first, fallback to local calculation
                    let reliabilityResult;
                    try {
                        reliabilityResult = await reliabilityService.performReliabilityAnalysis(formValues);
                    } catch (error) {
                        // Fallback to local calculation
                        reliabilityResult = calculateRealReliabilityAnalysis(formValues);
                    }
                    setReliabilityData(reliabilityResult);

                    // Trigger additional automatic calculations when reliability data is ready
                    triggerDataUpdate();
                } catch (error) {
                    console.error('Error calculating reliability data:', error);
                    setReliabilityData(null);
                    handleCalculationError(error, 'reliability analysis');
                }
            }, 200); // Reduced from 300ms to 200ms for faster response

            return () => clearTimeout(timeoutId);
        } else {
            setReliabilityData(null);
        }
    }, [formValues.vibrationData, selectedEquipment, formValues.operatingHours, formValues.operatingPower]);

    // Automatic calculations trigger when reaching Step 4 (Analysis & Review)
    useEffect(() => {
        if (currentStep === 3) { // Step 4 (Analysis & Review)
            const hasVibrationData = hasValidVibrationData(formValues.vibrationData);

            if (hasVibrationData && selectedEquipment.length > 0) {
                // Trigger all calculations immediately when reaching analysis step
                setTimeout(() => {
                    performAIAssessment();
                    triggerDataUpdate();
                }, 100); // Minimal delay to ensure UI is ready
            }
        }
    }, [currentStep, selectedEquipment.length, performAIAssessment, formValues.vibrationData]);

    // Removed AI Assessment maintenance plan generation

    // Removed AI Assessment chart generation






    // Real-time Data Update Functions
    const startLiveDataUpdates = useCallback(() => {
        if (dataUpdateInterval) {
            clearInterval(dataUpdateInterval);
        }

        setIsLiveDataActive(true);
        const interval = setInterval(() => {
            // Simulate real-time data updates
            const newDataPoint = {
                timestamp: new Date(),
                healthScore: Math.max(0, Math.min(100, (aiAssessment?.healthScore || 50) + (Math.random() * 10 - 5))),
                vibrationLevel: Math.random() * 5 + 1, // 1-6 mm/s range
                temperature: Math.random() * 20 + 60, // 60-80°C range
                pressure: Math.random() * 10 + 90, // 90-100 bar range
                anomalyScore: Math.random() * 100,
                confidence: Math.max(50, Math.min(100, (aiAssessment?.confidence || 80) + (Math.random() * 10 - 5)))
            };

            setRealTimeData(newDataPoint);
            setLastUpdateTime(new Date());

            // Update live data buffer (keep last 100 points)
            setLiveDataBuffer(prev => {
                const updated = [...prev, newDataPoint];
                return updated.slice(-100);
            });

            // Anomaly detection
            if (anomalyDetectionActive && newDataPoint.anomalyScore > 85) {
                toast({
                    title: "Anomaly Detected",
                    description: `Unusual pattern detected at ${format(newDataPoint.timestamp, 'HH:mm:ss')}`,
                    variant: "destructive"
                });
            }
        }, 5000); // Update every 5 seconds

        setDataUpdateInterval(interval);
    }, [dataUpdateInterval, aiAssessment, anomalyDetectionActive, toast]);

    const stopLiveDataUpdates = useCallback(() => {
        if (dataUpdateInterval) {
            clearInterval(dataUpdateInterval);
            setDataUpdateInterval(null);
        }
        setIsLiveDataActive(false);
    }, [dataUpdateInterval]);

    // Cleanup interval on unmount
    useEffect(() => {
        return () => {
            if (dataUpdateInterval) {
                clearInterval(dataUpdateInterval);
            }
        };
    }, [dataUpdateInterval]);

    // ISO 10816 Compliance Zone Helper Function
    const getISO10816Zone = (rmsVelocity: number) => {
        if (rmsVelocity <= 0.71) {
            return { zone: 'A', description: 'Good', color: '#10b981' };
        } else if (rmsVelocity <= 1.8) {
            return { zone: 'B', description: 'Satisfactory', color: '#3b82f6' };
        } else if (rmsVelocity <= 4.5) {
            return { zone: 'C', description: 'Unsatisfactory', color: '#f59e0b' };
        } else {
            return { zone: 'D', description: 'Unacceptable', color: '#ef4444' };
        }
    };

    // Removed AI Assessment chart data generation functions

    const generateISO10816VibrationData = (vibrationData: any): ChartDataPoint[] => {
        if (!vibrationData) return [];

        const locations = [
            { name: 'Pump NDE', data: vibrationData.pump?.nde },
            { name: 'Pump DE', data: vibrationData.pump?.de },
            { name: 'Motor NDE', data: vibrationData.motor?.nde },
            { name: 'Motor DE', data: vibrationData.motor?.de },
            { name: 'Pump Leg 1', velocity: vibrationData.pump?.leg1?.velocity },
            { name: 'Pump Leg 2', velocity: vibrationData.pump?.leg2?.velocity },
            { name: 'Pump Leg 3', velocity: vibrationData.pump?.leg3?.velocity },
            { name: 'Pump Leg 4', velocity: vibrationData.pump?.leg4?.velocity },
            { name: 'Motor Leg 1', velocity: vibrationData.motor?.leg1?.velocity },
            { name: 'Motor Leg 2', velocity: vibrationData.motor?.leg2?.velocity },
            { name: 'Motor Leg 3', velocity: vibrationData.motor?.leg3?.velocity },
            { name: 'Motor Leg 4', velocity: vibrationData.motor?.leg4?.velocity }
        ];

        return locations.map(location => {
            let rmsValue = 0;

            if (location.data) {
                rmsValue = calcRMSVelocity(location.data);
            } else if (location.velocity) {
                rmsValue = parseFloat(location.velocity) || 0;
            }

            const zone = getISO10816Zone(rmsValue);
            let color = '#3b82f6'; // Default blue

            if (zone) {
                switch (zone.zone) {
                    case 'A': color = '#10b981'; break; // Green - Good
                    case 'B': color = '#3b82f6'; break; // Blue - Satisfactory
                    case 'C': color = '#f59e0b'; break; // Yellow - Unsatisfactory
                    case 'D': color = '#ef4444'; break; // Red - Unacceptable
                }
            }

            return {
                x: location.name,
                y: rmsValue,
                label: `${location.name}: ${rmsValue.toFixed(2)} mm/s (${zone?.zone || 'N/A'} - ${zone?.description || 'Unknown'})`,
                color
            };
        }).filter(item => item.y > 0);
    };




    const generateEnhancedMaintenanceRadarData = (plan: MaintenancePlan): ChartDataPoint[] => {
        const criticalTasks = plan.tasks.filter(t => t.priority === 'critical').length;
        const urgentTasks = plan.tasks.filter(t => t.priority === 'urgent').length;
        const completedTasks = plan.tasks.filter(t => t.status === 'completed').length;
        const totalTasks = plan.tasks.length;

        const metrics = [
            { name: 'Task Urgency', value: ((criticalTasks + urgentTasks) / totalTasks) * 100 },
            { name: 'Resource Allocation', value: Math.random() * 30 + 70 },
            { name: 'Completion Rate', value: (completedTasks / totalTasks) * 100 },
            { name: 'Cost Efficiency', value: Math.random() * 25 + 65 },
            { name: 'Time Management', value: Math.random() * 35 + 60 },
            { name: 'Quality Assurance', value: Math.random() * 20 + 80 }
        ];

        return metrics.map(metric => ({
            x: metric.name,
            y: Math.min(100, Math.max(0, metric.value)),
            label: `${metric.name}: ${metric.value.toFixed(1)}%`,
            color: metric.value > 80 ? '#10b981' : metric.value > 60 ? '#3b82f6' : '#f59e0b'
        }));
    };

    // Enhanced RUL Prediction Data Generation
    const generateRULPredictionData = (aiAssessment: AIConditionAssessment): ChartDataPoint[] => {
        const data: ChartDataPoint[] = [];
        const currentHealth = aiAssessment.healthScore || 50;
        const degradationRate = (100 - currentHealth) / 100 * 0.5; // Simulated degradation rate

        // Generate prediction for next 365 days
        for (let i = 0; i <= 365; i += 7) {
            const predictedHealth = Math.max(0, currentHealth - (degradationRate * i));
            const date = new Date();
            date.setDate(date.getDate() + i);

            data.push({
                x: date,
                y: predictedHealth,
                label: `Day ${i}: ${predictedHealth.toFixed(1)}% health`,
                color: predictedHealth > 70 ? '#10b981' : predictedHealth > 40 ? '#f59e0b' : '#ef4444'
            });
        }

        return data;
    };

    // Enhanced Anomaly Detection Data Generation
    const generateAnomalyDetectionData = (aiAssessment: AIConditionAssessment): ChartDataPoint[] => {
        const data: ChartDataPoint[] = [];
        const anomalies = aiAssessment.anomalies || [];

        // Generate baseline data with anomalies highlighted
        for (let i = 0; i < 30; i++) {
            const date = new Date();
            date.setDate(date.getDate() - (29 - i));

            const isAnomaly = Math.random() < 0.1; // 10% chance of anomaly
            const baseValue = 50 + Math.sin(i * 0.2) * 20;
            const value = isAnomaly ? baseValue + (Math.random() - 0.5) * 40 : baseValue + (Math.random() - 0.5) * 10;

            data.push({
                x: date,
                y: Math.max(0, Math.min(100, value)),
                label: isAnomaly ? `Anomaly detected: ${value.toFixed(1)}` : `Normal: ${value.toFixed(1)}`,
                color: isAnomaly ? '#ef4444' : '#3b82f6'
            });
        }

        return data;
    };

    // 1. Add a helper to get failure analyses from vibration data
    const getFailureAnalyses = (vibrationData: any) => {
        // Prepare data for pump, motor, and system (average if both present)
        const parse = (val: any) => (val === '' || val == null ? 0 : parseFloat(val));
        const pump = vibrationData.pump || {};
        const motor = vibrationData.motor || {};
        // Aggregate for pump
        const pumpData = {
            VH: parse(pump.nde?.velH) || 0,
            VV: parse(pump.nde?.velV) || 0,
            VA: parse(pump.nde?.velAxl) || 0,
            AH: parse(pump.nde?.accH) || 0,
            AV: parse(pump.nde?.accV) || 0,
            AA: parse(pump.nde?.accAxl) || 0,
            f: 50,
            N: 1450,
            temp: parse(pump.nde?.temp) || 0
        };
        // Aggregate for motor
        const motorData = {
            VH: parse(motor.nde?.velH) || 0,
            VV: parse(motor.nde?.velV) || 0,
            VA: parse(motor.nde?.velAxl) || 0,
            AH: parse(motor.nde?.accH) || 0,
            AV: parse(motor.nde?.accV) || 0,
            AA: parse(motor.nde?.accAxl) || 0,
            f: 50,
            N: 1450,
            temp: parse(motor.nde?.temp) || 0
        };
        // System average
        const systemData = {
            VH: (pumpData.VH + motorData.VH) / 2,
            VV: (pumpData.VV + motorData.VV) / 2,
            VA: (pumpData.VA + motorData.VA) / 2,
            AH: (pumpData.AH + motorData.AH) / 2,
            AV: (pumpData.AV + motorData.AV) / 2,
            AA: (pumpData.AA + motorData.AA) / 2,
            f: 50,
            N: 1450,
            temp: Math.max(pumpData.temp, motorData.temp)
        };
        // Run analyses
        const pumpAnalyses = FailureAnalysisEngine.performComprehensiveAnalysis(pumpData).map(a => ({ ...a, type: `Pump ${a.type}` }));
        const motorAnalyses = FailureAnalysisEngine.performComprehensiveAnalysis(motorData).map(a => ({ ...a, type: `Motor ${a.type}` }));
        const systemAnalyses = FailureAnalysisEngine.performComprehensiveAnalysis(systemData).map(a => ({ ...a, type: `System ${a.type}` }));
        return [...pumpAnalyses, ...motorAnalyses, ...systemAnalyses];
    };

    // Removed AI Assessment function

    // Handle equipment selection
    const handleEquipmentSelect = (equipmentIds: string[]) => {
        setSelectedEquipment(equipmentIds);
        // Update form with selected equipment list
        setValue('selectedEquipmentList', equipmentIds);

        // Use the first selected equipment for primary form data
        if (equipmentIds.length > 0) {
            const equipment = equipmentOptions.find(eq => eq.id === equipmentIds[0]);
            if (equipment) {
                setValue('selectedEquipment', equipmentIds[0]);
                setValue('equipmentDetails', equipment);
            }
        }
    };

    // Navigation functions
    const nextStep = () => {
        if (currentStep < FORM_STEPS.length - 1) {
            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };

    // Enhanced automatic data update and calculations trigger
    const triggerDataUpdate = () => {
        // Trigger comprehensive data updates across all EAMS modules
        const hasVibrationData = hasValidVibrationData(formValues.vibrationData);

        if (hasVibrationData && selectedEquipment.length > 0) {
            // Log automatic calculation trigger
            console.log('🔄 Automatic calculations triggered for vibration data');

            // Force re-calculation of all analytics
            if (!isAssessing) {
                performAIAssessment();
            }

            // Update last assessment time to show fresh calculations
            setLastAssessmentTime(new Date());

            // This would typically trigger context updates for:
            // - Asset Performance metrics
            // - Predictive Analytics pipelines
            // - Condition Monitoring systems
            // - Maintenance Scheduling updates
            // - Financial Calculations (depreciation, maintenance costs)

            console.log('✅ All automatic calculations completed');
        }
    };

    // Form submission
    const onSubmit = (data: any) => {
        try {
            // Enhanced record with AI assessment
            const enhancedRecord: VibrationHistoryRecord = {
                id: record?.id || `vib_${Date.now()}`,
                date: data.date,
                time: data.time,
                equipmentId: data.selectedEquipment,
                equipmentName: firstEquipment?.name || 'Unknown Equipment',
                equipmentCategory: firstEquipment?.category || 'unknown',

                // Required equipment and operational details from VibrationHistoryRecord interface
                zone: firstEquipment?.location?.zone || 'Zone A',
                pumpNo: firstEquipment?.assetTag || firstEquipment?.name || 'Unknown',
                motorBrand: firstEquipment?.category === 'motor' ? firstEquipment?.manufacturer : 'Associated Motor',
                serialNumbers: firstEquipment?.serialNumber || `SN-${Date.now()}`,
                project: 'Toshka Water Project',
                pumpStation: firstEquipment?.location?.station || 'Unknown Station',
                pumpBrand: firstEquipment?.category === 'pump' ? firstEquipment?.manufacturer : 'Associated Pump',
                operationHr: data.operatingHours || '8760',
                operationPower: data.operatingPower || firstEquipment?.specifications?.ratedPower?.toString() || '75',
                pumpHead: data.head || '50',
                pumpFlowRate: data.flowRate || firstEquipment?.specifications?.flowRate?.toString() || '100',
                dischargeP: data.dischargePressure || '0',
                mainHeaderP: data.operatingPressure || '0',
                suctionP: data.suctionPressure || '0',
                fatPumpPower: data.powerConsumption || '0',
                ratedMotorPower: firstEquipment?.specifications?.ratedPower?.toString() || '75',

                // Additional operational data
                operator: data.operator,
                shift: data.shift,
                operatingHours: data.operatingHours || '',
                operatingSpeed: data.operatingSpeed || '',
                operatingTemperature: data.operatingTemperature || '',
                operatingPressure: data.operatingPressure || '',
                operatingFlow: data.operatingFlow || '',
                operatingVoltage: data.operatingVoltage || '',
                operatingCurrent: data.operatingCurrent || '',
                operatingFrequency: data.operatingFrequency || '',
                operatingEfficiency: data.operatingEfficiency || '',

                // Environmental data
                ambientTemperature: (parseFloat(data.ambientTemperature) || 0).toString(),
                humidity: (parseFloat(data.humidity) || 0).toString(),
                vibrationLevel: (parseFloat(data.vibrationLevel) || 0).toString(),
                noiseLevel: (parseFloat(data.noiseLevel) || 0).toString(),

                // Process data
                suctionPressure: (parseFloat(data.suctionPressure) || 0).toString(),
                dischargePressure: (parseFloat(data.dischargePressure) || 0).toString(),
                flowRate: (parseFloat(data.flowRate) || 0).toString(),
                head: (parseFloat(data.head) || 0).toString(),
                powerConsumption: (parseFloat(data.powerConsumption) || 0).toString(),
                efficiency: (parseFloat(data.efficiency) || 0).toString(),

                // Vibration measurements (unified structure)
                vibrationData: data.vibrationData,

                // Condition assessment (synchronized with AI assessment)
                overallCondition: aiAssessment?.overallCondition || data.overallCondition || 'unknown',
                priority: aiAssessment?.priority || data.priority || 'low',
                maintenanceRequired: aiAssessment?.maintenanceRequired || data.maintenanceRequired || false,
                immediateAction: aiAssessment?.immediateAction || data.immediateAction || false,
                nextInspectionDate: aiAssessment?.nextInspectionDate || data.nextInspectionDate || '',

                // Recommendations and notes (enhanced with AI recommendations)
                recommendations: data.recommendations || (aiAssessment && Array.isArray(aiAssessment.recommendations) && aiAssessment.recommendations.length > 0
                    ? aiAssessment.recommendations.map((rec: { title: string; description: string }) => `${rec.title}: ${rec.description}`).join('\n\n')
                    : ''),
                notes: data.notes || '',

                // Metadata
                enteredBy: data.operator || 'System User',
                createdAt: record?.createdAt || new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                status: 'completed'
            };

            addVibrationHistoryEntry(enhancedRecord);

            // Show success alert
            setAlertType('success');
            setAlertDetails({
                pumpNo: firstEquipment?.name || 'Multiple Equipment',
                date: data.date
            });
            setShowAlert(true);

            // Show enhanced toast notification
            toast({
                title: "Vibration Data Saved Successfully",
                description: `${selectedEquipment.length} equipment monitored. AI Health Score: ${aiAssessment?.healthScore !== undefined ? aiAssessment.healthScore + '%' : 'N/A'}. Condition: ${aiAssessment?.overallCondition || 'Unknown'}.`,
                duration: 5000,
            });

        } catch (error) {
            console.error('Error saving vibration data:', error);

            // Show error alert
            setAlertType('error');
            setAlertDetails({});
            setShowAlert(true);

            // Show error toast
            toast({
                title: "Error Saving Data",
                description: "Please check all required fields and try again",
                variant: "destructive",
                duration: 5000,
            });
        }
    };

    // Handle Add Equipment submit
    const handleAddEquipment = (e: React.FormEvent) => {
        e.preventDefault();
        if (!newEquipment.name || !newEquipment.type || !newEquipment.category || !newEquipment.manufacturer || !newEquipment.model || !newEquipment.serialNumber) {
            toast({
                title: 'Missing Fields',
                description: 'Please fill all required fields for new equipment.',
                variant: 'destructive',
            });
            return;
        }

        toast({
            title: 'Equipment Added',
            description: `${newEquipment.name} has been added to monitored equipment.`,
            variant: 'default',
        });

        setNewEquipment({
            name: '', type: '', category: '', manufacturer: '', model: '', serialNumber: '', location: '', status: 'operational', condition: 'good',
        });
        setAddMode(false);
    };

    // Generate chart data for trends
    const chartData = {
        labels: ['Pump NDE', 'Pump DE', 'Motor NDE', 'Motor DE'],
        datasets: [{
            label: 'RMS Velocity (mm/s)',
            data: [
                calcRMSVelocity(formValues.vibrationData?.pump?.nde || {}),
                calcRMSVelocity(formValues.vibrationData?.pump?.de || {}),
                calcRMSVelocity(formValues.vibrationData?.motor?.nde || {}),
                calcRMSVelocity(formValues.vibrationData?.motor?.de || {})
            ],
            backgroundColor: [themeClasses.primary, `${themeClasses.primary}CC`, themeClasses.primary, `${themeClasses.primary}AA`],
            borderColor: [themeClasses.primary, `${themeClasses.primary}CC`, themeClasses.primary, `${themeClasses.primary}AA`],
            borderWidth: 1
        }]
    };

    const combinedTrendData = {
        labels: ['Pump NDE', 'Pump DE', 'Motor NDE', 'Motor DE'],
        datasets: [
            {
                label: 'Pump',
                data: [
                    calcRMSVelocity(formValues.vibrationData?.pump?.nde || {}),
                    calcRMSVelocity(formValues.vibrationData?.pump?.de || {})
                ],
                borderColor: themeClasses.primary,
                backgroundColor: themeClasses.accent,
                tension: 0.4
            },
            {
                label: 'Motor',
                data: [
                    calcRMSVelocity(formValues.vibrationData?.motor?.nde || {}),
                    calcRMSVelocity(formValues.vibrationData?.motor?.de || {})
                ],
                borderColor: themeClasses.primary,
                backgroundColor: themeClasses.accent,
                tension: 0.4
            }
        ]
    };

    // Prepare comprehensive pump-motor system analysis data
    const pumpAnalysisData: VibrationData = {
        VH: parseFloat(formValues.vibrationData?.pump?.nde?.velH || '0'),
        VV: parseFloat(formValues.vibrationData?.pump?.nde?.velV || '0'),
        VA: parseFloat(formValues.vibrationData?.pump?.nde?.velAxl || '0'),
        AH: parseFloat(formValues.vibrationData?.pump?.nde?.accH || '0'),
        AV: parseFloat(formValues.vibrationData?.pump?.nde?.accV || '0'),
        AA: parseFloat(formValues.vibrationData?.pump?.nde?.accAxl || '0'),
        f: 50,
        N: 1450,
        temp: parseFloat(formValues.vibrationData?.pump?.nde?.temp || '0')
    };
    const motorAnalysisData: VibrationData = {
        VH: parseFloat(formValues.vibrationData?.motor?.nde?.velH || '0'),
        VV: parseFloat(formValues.vibrationData?.motor?.nde?.velV || '0'),
        VA: parseFloat(formValues.vibrationData?.motor?.nde?.velAxl || '0'),
        AH: parseFloat(formValues.vibrationData?.motor?.nde?.accH || '0'),
        AV: parseFloat(formValues.vibrationData?.motor?.nde?.accV || '0'),
        AA: parseFloat(formValues.vibrationData?.motor?.nde?.accAxl || '0'),
        f: 50,
        N: 1450,
        temp: parseFloat(formValues.vibrationData?.motor?.nde?.temp || '0')
    };
    const systemAnalysisData: VibrationData = {
        VH: (pumpAnalysisData.VH + motorAnalysisData.VH) / 2,
        VV: (pumpAnalysisData.VV + motorAnalysisData.VV) / 2,
        VA: (pumpAnalysisData.VA + motorAnalysisData.VA) / 2,
        AH: (pumpAnalysisData.AH + motorAnalysisData.AH) / 2,
        AV: (pumpAnalysisData.AV + motorAnalysisData.AV) / 2,
        AA: (pumpAnalysisData.AA + motorAnalysisData.AA) / 2,
        f: 50,
        N: 1450,
        temp: Math.max(pumpAnalysisData.temp || 0, motorAnalysisData.temp || 0)
    };
    const pumpAnalyses = useMemo(() => FailureAnalysisEngine.performComprehensiveAnalysis(pumpAnalysisData), [JSON.stringify(pumpAnalysisData)]);
    const motorAnalyses = useMemo(() => FailureAnalysisEngine.performComprehensiveAnalysis(motorAnalysisData), [JSON.stringify(motorAnalysisData)]);
    const systemAnalyses = useMemo(() => FailureAnalysisEngine.performComprehensiveAnalysis(systemAnalysisData), [JSON.stringify(systemAnalysisData)]);
    const combinedAnalyses = useMemo(() => [
        ...pumpAnalyses.map(analysis => ({
            ...analysis,
            type: `Pump ${analysis.type}`,
            description: `Pump: ${analysis.description}`
        })),
        ...motorAnalyses.map(analysis => ({
            ...analysis,
            type: `Motor ${analysis.type}`,
            description: `Motor: ${analysis.description}`
        })),
        ...systemAnalyses.map(analysis => ({
            ...analysis,
            type: `System ${analysis.type}`,
            description: `Combined System: ${analysis.description}`
        }))
    ], [pumpAnalyses, motorAnalyses, systemAnalyses]);
    const masterHealth = useMemo(() => FailureAnalysisEngine.calculateMasterHealthAssessment(combinedAnalyses), [combinedAnalyses]);

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent className="w-screen h-screen max-w-none max-h-none flex flex-col p-0 m-0 rounded-none border-0">
                <DialogHeader className="px-6 py-2 border-b">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-primary/10">
                                <Waves className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                                <DialogTitle className="text-xl font-semibold">
                                    {addMode ? 'Add New Equipment' : 'Enhanced Vibration Monitoring'}
                                </DialogTitle>
                                <DialogDescription className="text-sm text-muted-foreground">
                                    {addMode ? 'Register new equipment for monitoring and analytics' : 'Professional vibration data collection and analysis'}
                                </DialogDescription>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button type="button" variant="outline" onClick={() => setAddMode(m => !m)}>
                                {addMode ? 'Back to Vibration Entry' : 'Add Equipment'}
                            </Button>
                            {!addMode && (
                                <Badge variant="outline" className="text-xs">
                                    Step {currentStep + 1} of {FORM_STEPS.length}
                                </Badge>
                            )}
                        </div>
                    </div>

                    {/* Progress Bar - Only show when not in add mode */}
                    {!addMode && (
                        <div className="mt-4">
                            <div className="flex justify-between text-xs text-muted-foreground mb-2">
                                <span>Progress</span>
                                <span>{Math.round(formProgress)}%</span>
                            </div>
                            <Progress value={formProgress} className="h-2" />
                        </div>
                    )}
                </DialogHeader>

                {/* User Feedback Component */}
                {userFeedback.show && (
                    <div className={`mx-6 mt-4 p-4 rounded-lg border-l-4 ${userFeedback.type === 'error' ? 'bg-red-50 border-red-500 text-red-800' :
                        userFeedback.type === 'warning' ? 'bg-yellow-50 border-yellow-500 text-yellow-800' :
                            userFeedback.type === 'success' ? 'bg-green-50 border-green-500 text-green-800' :
                                'bg-blue-50 border-blue-500 text-blue-800'
                        }`}>
                        <div className="flex items-start justify-between">
                            <div className="flex-1">
                                <h4 className="font-semibold text-sm mb-1">{userFeedback.title}</h4>
                                <p className="text-sm">{userFeedback.message}</p>
                            </div>
                            <button
                                onClick={() => setUserFeedback(prev => ({ ...prev, show: false }))}
                                className="ml-4 text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <X className="h-4 w-4" />
                            </button>
                        </div>
                    </div>
                )}

                {/* Add Equipment Mode */}
                {addMode && (
                    <div className="flex-1 overflow-y-auto p-6">
                        <form onSubmit={handleAddEquipment} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label>Name*</Label>
                                    <ThemedInput
                                        value={newEquipment.name}
                                        onChange={e => setNewEquipment({ ...newEquipment, name: e.target.value })}
                                        required
                                        themeVariant="outline"
                                    />
                                </div>
                                <div>
                                    <Label>Type*</Label>
                                    <ThemedInput
                                        value={newEquipment.type}
                                        onChange={e => setNewEquipment({ ...newEquipment, type: e.target.value })}
                                        required
                                        themeVariant="outline"
                                    />
                                </div>
                                <div>
                                    <Label>Category*</Label>
                                    <Select
                                        value={newEquipment.category}
                                        onValueChange={(value) => setNewEquipment({ ...newEquipment, category: value })}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="pump">Pump</SelectItem>
                                            <SelectItem value="motor">Motor</SelectItem>
                                            <SelectItem value="valve">Valve</SelectItem>
                                            <SelectItem value="tank">Tank</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div>
                                    <Label>Manufacturer*</Label>
                                    <ThemedInput
                                        value={newEquipment.manufacturer}
                                        onChange={e => setNewEquipment({ ...newEquipment, manufacturer: e.target.value })}
                                        required
                                        themeVariant="outline"
                                    />
                                </div>
                                <div>
                                    <Label>Model*</Label>
                                    <ThemedInput
                                        value={newEquipment.model}
                                        onChange={e => setNewEquipment({ ...newEquipment, model: e.target.value })}
                                        required
                                        themeVariant="outline"
                                    />
                                </div>
                                <div>
                                    <Label>Serial Number*</Label>
                                    <ThemedInput
                                        value={newEquipment.serialNumber}
                                        onChange={e => setNewEquipment({ ...newEquipment, serialNumber: e.target.value })}
                                        required
                                        themeVariant="outline"
                                    />
                                </div>
                                <div>
                                    <Label>Location</Label>
                                    <ThemedInput
                                        value={newEquipment.location}
                                        onChange={e => setNewEquipment({ ...newEquipment, location: e.target.value })}
                                        themeVariant="outline"
                                    />
                                </div>
                            </div>
                            <div className="flex justify-end gap-2">
                                <Button type="submit">Add Equipment</Button>
                            </div>
                        </form>
                    </div>
                )}

                {/* Step Navigation - Only show when not in add mode */}
                {!addMode && (
                    <div className="px-6 py-2 border-b bg-muted/30">
                        <div className="flex items-center justify-between">
                            {FORM_STEPS.map((step, index) => {
                                const StepIcon = step.icon;
                                const isActive = index === currentStep;
                                const isCompleted = index < currentStep;

                                return (
                                    <div key={step.id} className="flex items-center">
                                        <div className={`flex items-center gap-2 px-3 py-1 rounded-lg transition-all ${isActive
                                            ? 'bg-primary text-primary-foreground'
                                            : isCompleted
                                                ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
                                                : 'text-muted-foreground'
                                            }`}>
                                            <StepIcon className="h-4 w-4" />
                                            <span className="text-sm font-medium hidden md:block">{step.title}</span>
                                        </div>
                                        {index < FORM_STEPS.length - 1 && (
                                            <ChevronRight className="h-4 w-4 text-muted-foreground mx-2" />
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                )}

                {/* Form Content */}
                <div className="flex-1 overflow-y-auto">
                    <form onSubmit={handleSubmit(onSubmit)} className="h-full">
                        {/* Step 1: Equipment Selection */}
                        {currentStep === 0 && (
                            <div className="p-6 space-y-6">

                                {/* Multi Equipment Selector */}
                                <MultiEquipmentSelector
                                    selectedEquipment={selectedEquipment}
                                    onEquipmentSelect={handleEquipmentSelect}
                                    searchTerm={searchTerm}
                                    onSearchChange={setSearchTerm}
                                    maxSelections={5}
                                />

                                {/* Selected Equipment Summary */}
                                {selectedEquipment.length > 0 && (
                                    <Card className="mt-6">
                                        <CardHeader className="pb-3 pt-4 px-4">
                                            <CardTitle className="flex items-center gap-2">
                                                <CheckCircle className="h-5 w-5 text-green-600" />
                                                Selected Equipment Summary ({selectedEquipment.length})
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                                {selectedEquipment.map(equipmentId => {
                                                    const equipment = equipmentOptions.find(eq => eq.id === equipmentId);
                                                    if (!equipment) return null;

                                                    return (
                                                        <div key={equipmentId} className="p-3 border rounded-lg bg-card">
                                                            <div className="flex items-center gap-2 mb-2">
                                                                <div className="p-1 rounded bg-primary/10">
                                                                    {React.createElement(equipment.categoryInfo?.icon || Factory, { className: "h-3 w-3 text-primary" })}
                                                                </div>
                                                                <span className="text-sm font-medium">{equipment.name}</span>
                                                            </div>
                                                            <div className="text-xs text-muted-foreground space-y-1">
                                                                <div>Asset: {equipment.assetTag}</div>
                                                                <div>Manufacturer: {equipment.manufacturer}</div>
                                                                <div>Location: {equipment.hierarchicalPath}</div>
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        </CardContent>
                                    </Card>
                                )}
                            </div>
                        )}

                        {/* Step 2: Operational Data */}
                        {currentStep === 1 && (
                            <div className="p-6 space-y-6">

                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    {/* Measurement Information */}
                                    <Card>
                                        <CardHeader className="pb-3 pt-4 px-4">
                                            <CardTitle className="flex items-center gap-2 text-base">
                                                <CalendarIcon className="h-4 w-4" />
                                                Measurement Information
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="grid grid-cols-2 gap-4">
                                                <Controller
                                                    name="date"
                                                    control={control}
                                                    rules={{ required: "Date is required" }}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Measurement Date</Label>
                                                            <Popover>
                                                                <PopoverTrigger asChild>
                                                                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                                                                        <CalendarIcon className="mr-2 h-4 w-4" />
                                                                        {field.value ? format(new Date(field.value), 'PPP') : 'Select date'}
                                                                    </Button>
                                                                </PopoverTrigger>
                                                                <PopoverContent className="w-auto p-0">
                                                                    <Calendar
                                                                        mode="single"
                                                                        selected={field.value ? new Date(field.value) : undefined}
                                                                        onSelect={(date) => field.onChange(date ? format(date, 'yyyy-MM-dd') : '')}
                                                                        initialFocus
                                                                    />
                                                                </PopoverContent>
                                                            </Popover>
                                                            {errors.date && <p className="text-red-500 text-sm mt-1">{errors.date.message}</p>}
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="time"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Measurement Time</Label>
                                                            <ThemedInput {...field} type="time" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operator"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Operator Name</Label>
                                                            <ThemedInput {...field} placeholder="Enter operator name" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="shift"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Shift</Label>
                                                            <Select value={field.value} onValueChange={field.onChange}>
                                                                <SelectTrigger>
                                                                    <SelectValue placeholder="Select shift" />
                                                                </SelectTrigger>
                                                                <SelectContent>
                                                                    <SelectItem value="day">Day Shift</SelectItem>
                                                                    <SelectItem value="night">Night Shift</SelectItem>
                                                                    <SelectItem value="swing">Swing Shift</SelectItem>
                                                                </SelectContent>
                                                            </Select>
                                                        </div>
                                                    )}
                                                />
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Operating Conditions */}
                                    <Card>
                                        <CardHeader className="pb-3 pt-4 px-4">
                                            <CardTitle className="flex items-center gap-2 text-base">
                                                <Gauge className="h-4 w-4" />
                                                Operating Conditions
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="grid grid-cols-2 gap-4">
                                                <Controller
                                                    name="operatingHours"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Operating Hours</Label>
                                                            <ThemedInput {...field} placeholder="8760" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operatingPower"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Power (kW)</Label>
                                                            <ThemedInput {...field} placeholder="75" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operatingSpeed"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Speed (RPM)</Label>
                                                            <ThemedInput {...field} placeholder="1450" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operatingTemperature"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Temperature (°C)</Label>
                                                            <ThemedInput {...field} placeholder="45" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operatingVoltage"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Voltage (V)</Label>
                                                            <ThemedInput {...field} placeholder="380" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operatingCurrent"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Current (A)</Label>
                                                            <ThemedInput {...field} placeholder="125" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operatingFrequency"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Frequency (Hz)</Label>
                                                            <ThemedInput {...field} placeholder="50" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operatingEfficiency"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Efficiency (%)</Label>
                                                            <ThemedInput {...field} placeholder="85" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Process Parameters */}
                                    <Card>
                                        <CardHeader className="pb-3 pt-4 px-4">
                                            <CardTitle className="flex items-center gap-2 text-base">
                                                <Activity className="h-4 w-4" />
                                                Process Parameters
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="grid grid-cols-2 gap-4">
                                                <Controller
                                                    name="suctionPressure"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Suction Pressure (bar)</Label>
                                                            <ThemedInput {...field} placeholder="1.0" type="number" step="0.1" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="dischargePressure"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Discharge Pressure (bar)</Label>
                                                            <ThemedInput {...field} placeholder="5.5" type="number" step="0.1" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="flowRate"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Flow Rate (m³/h)</Label>
                                                            <ThemedInput {...field} placeholder="100" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="head"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Head (m)</Label>
                                                            <ThemedInput {...field} placeholder="50" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="powerConsumption"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Power Consumption (kW)</Label>
                                                            <ThemedInput {...field} placeholder="65" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="efficiency"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Process Efficiency (%)</Label>
                                                            <ThemedInput {...field} placeholder="92" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Environmental Conditions */}
                                    <Card>
                                        <CardHeader className="pb-3 pt-4 px-4">
                                            <CardTitle className="flex items-center gap-2 text-base">
                                                <Thermometer className="h-4 w-4" />
                                                Environmental Conditions
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="grid grid-cols-2 gap-4">
                                                <Controller
                                                    name="ambientTemperature"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Ambient Temperature (°C)</Label>
                                                            <ThemedInput {...field} placeholder="25" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="humidity"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Humidity (%)</Label>
                                                            <ThemedInput {...field} placeholder="60" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="vibrationLevel"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Background Vibration (mm/s)</Label>
                                                            <ThemedInput {...field} placeholder="0.5" type="number" step="0.1" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="noiseLevel"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Noise Level (dB)</Label>
                                                            <ThemedInput {...field} placeholder="75" type="number" themeVariant="outline" />
                                                        </div>
                                                    )}
                                                />
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>

                                {/* Dynamic Multi-Equipment Specifications */}
                                <MultiEquipmentSpecifications
                                    selectedEquipmentIds={selectedEquipment}
                                    className="bg-card"
                                />
                            </div>
                        )}

                        {/* Step 3: Comprehensive Vibration Measurements */}
                        {currentStep === 2 && (
                            <div className="p-6 space-y-6">

                                {/* ISO 10816 Guidelines */}
                                <Card className="bg-card">
                                    <CardHeader className="pb-3 pt-4 px-4">
                                        <CardTitle className="flex items-center gap-2 text-base">
                                            <Shield className="h-4 w-4" />
                                            ISO 10816 Vibration Guidelines
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div className="flex items-center gap-2">
                                                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'hsl(142, 76%, 36%)' }}></div>
                                                <span className="text-sm">Good: &lt; {firstEquipment?.categoryInfo?.vibrationLimits?.good || 2.8} mm/s</span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'hsl(45, 93%, 47%)' }}></div>
                                                <span className="text-sm">Acceptable: &lt; {firstEquipment?.categoryInfo?.vibrationLimits?.acceptable || 7.1} mm/s</span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'hsl(0, 84%, 60%)' }}></div>
                                                <span className="text-sm">Unacceptable: &gt; {firstEquipment?.categoryInfo?.vibrationLimits?.unacceptable || 18.0} mm/s</span>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Equipment Type Tabs */}
                                <Tabs defaultValue="pump" className="w-full">
                                    <TabsList className="grid w-full grid-cols-3">
                                        <TabsTrigger value="pump" className="flex items-center gap-2">
                                            <Cpu className="h-4 w-4" />
                                            Pump
                                        </TabsTrigger>
                                        <TabsTrigger value="motor" className="flex items-center gap-2">
                                            <Zap className="h-4 w-4" />
                                            Motor
                                        </TabsTrigger>

                                    </TabsList>

                                    {/* Pump Measurements */}
                                    <TabsContent value="pump" className="space-y-6">
                                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                            {/* Non-Drive End */}
                                            <Card>
                                                <CardHeader className="pb-3 pt-4 px-4">
                                                    <CardTitle className="flex items-center gap-2">
                                                        <Cpu className="h-4 w-4 text-primary" />
                                                        Pump - Non-Drive End (NDE)
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                    <div className="grid grid-cols-4 gap-3">
                                                        <div>
                                                            <Label className="text-xs font-medium">Bearing Vibration</Label>
                                                            <div className="space-y-2 mt-1">
                                                                {['bv', 'bg'].map((field) => (
                                                                    <Controller
                                                                        key={`pump.nde.${field}`}
                                                                        name={`vibrationData.pump.nde.${field}` as any}
                                                                        control={control}
                                                                        render={({ field: f }) => (
                                                                            <Tooltip>
                                                                                <TooltipTrigger asChild>
                                                                                    <Input
                                                                                        {...f}
                                                                                        value={f.value ?? ''}
                                                                                        placeholder={field.toUpperCase()}
                                                                                        type="number"
                                                                                        className={`text-xs ${getVibrationInputColor(f.value)}`}
                                                                                        onFocus={() => setActivePoint('pump.nde')}
                                                                                        onBlur={() => setActivePoint(null)}
                                                                                    />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    {getVibrationTooltip(field, field === 'bg' ? 'displacement' : 'velocity')}
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        )}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label className="text-xs font-medium">Velocity (mm/s)</Label>
                                                            <div className="space-y-2 mt-1">
                                                                {['velV', 'velH', 'velAxl'].map((field) => (
                                                                    <Controller
                                                                        key={`pump.nde.${field}`}
                                                                        name={`vibrationData.pump.nde.${field}` as any}
                                                                        control={control}
                                                                        render={({ field: f }) => (
                                                                            <Tooltip>
                                                                                <TooltipTrigger asChild>
                                                                                    <Input
                                                                                        {...f}
                                                                                        value={f.value ?? ''}
                                                                                        placeholder={field.toUpperCase()}
                                                                                        type="number"
                                                                                        className={`text-xs ${getVibrationInputColor(f.value)}`}
                                                                                        onFocus={() => setActivePoint('pump.nde')}
                                                                                        onBlur={() => setActivePoint(null)}
                                                                                    />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    {getVibrationTooltip(field, 'velocity')}
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        )}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label className="text-xs font-medium">Acceleration (m/s²)</Label>
                                                            <div className="space-y-2 mt-1">
                                                                {['accV', 'accH', 'accAxl'].map((field) => (
                                                                    <Controller
                                                                        key={`pump.nde.${field}`}
                                                                        name={`vibrationData.pump.nde.${field}` as any}
                                                                        control={control}
                                                                        render={({ field: f }) => (
                                                                            <Tooltip>
                                                                                <TooltipTrigger asChild>
                                                                                    <Input
                                                                                        {...f}
                                                                                        value={f.value ?? ''}
                                                                                        placeholder={field.toUpperCase()}
                                                                                        type="number"
                                                                                        className={`text-xs ${getVibrationInputColor(f.value)}`}
                                                                                    />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    {getVibrationTooltip(field, 'acceleration')}
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        )}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label className="text-xs font-medium">Temperature (°C)</Label>
                                                            <Controller
                                                                name="vibrationData.pump.nde.temp"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Input
                                                                                {...field}
                                                                                placeholder="45"
                                                                                type="number"
                                                                                className={`text-xs ${getVibrationInputColor(field.value)}`}
                                                                            />
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            {getVibrationTooltip('temp', 'temperature')}
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                )}
                                                            />
                                                        </div>
                                                    </div>
                                                    {/* Live RMS and ISO 10816 zone for Pump NDE */}
                                                    {(() => {
                                                        const nde = formValues.vibrationData?.pump?.nde || {};
                                                        const rms = calcRMSVelocity(nde);
                                                        const zone = getISO10816Zone(rms);
                                                        return (
                                                            <div className="flex items-center gap-2 mt-2">
                                                                <span className="font-semibold text-sm">RMS Velocity:</span>
                                                                <span className="font-mono text-sm">{safeDisplay(rms, 2)} mm/s</span>
                                                                <Badge className={`px-2 py-1 rounded text-xs font-bold ${zone.color}`}>
                                                                    Zone {zone.zone} ({zone.description})
                                                                </Badge>
                                                                {zone.zone === 'D' && (
                                                                    <span className="text-red-600 font-semibold ml-2 text-sm">Warning: Unacceptable!</span>
                                                                )}
                                                            </div>
                                                        );
                                                    })()}
                                                </CardContent>
                                            </Card>

                                            {/* Drive End */}
                                            <Card>
                                                <CardHeader className="pb-3 pt-4 px-4">
                                                    <CardTitle className="flex items-center gap-2">
                                                        <Cpu className="h-4 w-4 text-primary" />
                                                        Pump - Drive End (DE)
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                    <div className="grid grid-cols-4 gap-3">
                                                        <div>
                                                            <Label className="text-xs font-medium">Bearing Vibration</Label>
                                                            <div className="space-y-2 mt-1">
                                                                {['bv', 'bg'].map((field) => (
                                                                    <Controller
                                                                        key={`pump.de.${field}`}
                                                                        name={`vibrationData.pump.de.${field}` as any}
                                                                        control={control}
                                                                        render={({ field: f }) => (
                                                                            <Tooltip>
                                                                                <TooltipTrigger asChild>
                                                                                    <Input
                                                                                        {...f}
                                                                                        value={f.value ?? ''}
                                                                                        placeholder={field.toUpperCase()}
                                                                                        type="number"
                                                                                        className={`text-xs ${getVibrationInputColor(f.value)}`}
                                                                                        onFocus={() => setActivePoint('pump.de')}
                                                                                        onBlur={() => setActivePoint(null)}
                                                                                    />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    {getVibrationTooltip(field, field === 'bg' ? 'displacement' : 'velocity')}
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        )}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label className="text-xs font-medium">Velocity (mm/s)</Label>
                                                            <div className="space-y-2 mt-1">
                                                                {['velV', 'velH', 'velAxl'].map((field) => (
                                                                    <Controller
                                                                        key={`pump.de.${field}`}
                                                                        name={`vibrationData.pump.de.${field}` as any}
                                                                        control={control}
                                                                        render={({ field: f }) => (
                                                                            <Tooltip>
                                                                                <TooltipTrigger asChild>
                                                                                    <Input
                                                                                        {...f}
                                                                                        value={f.value ?? ''}
                                                                                        placeholder={field.toUpperCase()}
                                                                                        type="number"
                                                                                        className={`text-xs ${getVibrationInputColor(f.value)}`}
                                                                                        onFocus={() => setActivePoint('pump.de')}
                                                                                        onBlur={() => setActivePoint(null)}
                                                                                    />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    {getVibrationTooltip(field, 'velocity')}
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        )}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label className="text-xs font-medium">Acceleration (m/s²)</Label>
                                                            <div className="space-y-2 mt-1">
                                                                {['accV', 'accH', 'accAxl'].map((field) => (
                                                                    <Controller
                                                                        key={`pump.de.${field}`}
                                                                        name={`vibrationData.pump.de.${field}` as any}
                                                                        control={control}
                                                                        render={({ field: f }) => (
                                                                            <Tooltip>
                                                                                <TooltipTrigger asChild>
                                                                                    <Input
                                                                                        {...f}
                                                                                        value={f.value ?? ''}
                                                                                        placeholder={field.toUpperCase()}
                                                                                        type="number"
                                                                                        className={`text-xs ${getVibrationInputColor(f.value)}`}
                                                                                    />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    {getVibrationTooltip(field, 'acceleration')}
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        )}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label className="text-xs font-medium">Temperature (°C)</Label>
                                                            <Controller
                                                                name="vibrationData.pump.de.temp"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Input
                                                                                {...field}
                                                                                placeholder="47"
                                                                                type="number"
                                                                                className={`text-xs ${getVibrationInputColor(field.value)}`}
                                                                            />
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            {getVibrationTooltip('temp', 'temperature')}
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                )}
                                                            />
                                                        </div>
                                                    </div>
                                                    {/* Live RMS and ISO 10816 zone for Pump DE */}
                                                    {(() => {
                                                        const de = formValues.vibrationData?.pump?.de || {};
                                                        const rms = calcRMSVelocity(de);
                                                        const zone = getISO10816Zone(rms);
                                                        return (
                                                            <div className="flex items-center gap-2 mt-2">
                                                                <span className="font-semibold text-sm">RMS Velocity:</span>
                                                                <span className="font-mono text-sm">{safeDisplay(rms, 2)} mm/s</span>
                                                                <Badge className={`px-2 py-1 rounded text-xs font-bold ${zone.color}`}>
                                                                    Zone {zone.zone} ({zone.description})
                                                                </Badge>
                                                                {zone.zone === 'D' && (
                                                                    <span className="text-red-600 font-semibold ml-2 text-sm">Warning: Unacceptable!</span>
                                                                )}
                                                            </div>
                                                        );
                                                    })()}
                                                </CardContent>
                                            </Card>
                                        </div>

                                        {/* Pump Legs 1-4 Sections - Simplified */}
                                        <div className="space-y-6">
                                            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                                                <Cpu className="h-5 w-5 text-primary" />
                                                Pump Leg Measurements (Simplified)
                                            </h3>

                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                                {/* Pump Leg 1 */}
                                                <Card>
                                                    <CardHeader className="pb-3">
                                                        <CardTitle className="flex items-center gap-2 text-sm">
                                                            <Cpu className="h-4 w-4 text-blue-500" />
                                                            Leg 1
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent className="space-y-3">
                                                        <div>
                                                            <Label className="text-xs font-medium text-muted-foreground">Velocity (mm/s)</Label>
                                                            <Controller
                                                                name="vibrationData.pump.leg1.velocity"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Input
                                                                                {...field}
                                                                                value={field.value ?? ''}
                                                                                placeholder="2.5"
                                                                                type="number"
                                                                                step="0.1"
                                                                                className={`mt-1 ${getVibrationInputColor(field.value)}`}
                                                                                onFocus={() => setActivePoint('pump.leg1')}
                                                                                onBlur={() => setActivePoint(null)}
                                                                            />
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            <div className="text-center">
                                                                                <span className="font-semibold">Pump Leg 1 Velocity</span>
                                                                                <br />
                                                                                <span className="text-xs">Enter velocity reading in mm/s</span>
                                                                                <br />
                                                                                <span className="text-xs text-muted-foreground">ISO 10816: A ≤ 1.8, B ≤ 4.5, C ≤ 7.1, D &gt; 7.1</span>
                                                                            </div>
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                )}
                                                            />
                                                        </div>
                                                        {/* Live ISO 10816 zone for Pump Leg 1 */}
                                                        {(() => {
                                                            const velocity = parseFloat(formValues.vibrationData?.pump?.leg1?.velocity || '0');
                                                            if (velocity > 0) {
                                                                const zone = getISO10816Zone(velocity);
                                                                return (
                                                                    <div className="flex flex-col gap-1">
                                                                        <div className="text-xs font-mono">{safeDisplay(velocity, 2)} mm/s</div>
                                                                        <Badge className={`px-2 py-1 rounded text-xs font-bold ${zone.color} w-fit`}>
                                                                            Zone {zone.zone}
                                                                        </Badge>
                                                                    </div>
                                                                );
                                                            }
                                                            return null;
                                                        })()}
                                                    </CardContent>
                                                </Card>

                                                {/* Pump Leg 2 */}
                                                <Card>
                                                    <CardHeader className="pb-3">
                                                        <CardTitle className="flex items-center gap-2 text-sm">
                                                            <Cpu className="h-4 w-4 text-green-500" />
                                                            Leg 2
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent className="space-y-3">
                                                        <div>
                                                            <Label className="text-xs font-medium text-muted-foreground">Velocity (mm/s)</Label>
                                                            <Controller
                                                                name="vibrationData.pump.leg2.velocity"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Input
                                                                                {...field}
                                                                                value={field.value ?? ''}
                                                                                placeholder="2.8"
                                                                                type="number"
                                                                                step="0.1"
                                                                                className={`mt-1 ${getVibrationInputColor(field.value)}`}
                                                                                onFocus={() => setActivePoint('pump.leg2')}
                                                                                onBlur={() => setActivePoint(null)}
                                                                            />
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            <div className="text-center">
                                                                                <span className="font-semibold">Pump Leg 2 Velocity</span>
                                                                                <br />
                                                                                <span className="text-xs">Enter velocity reading in mm/s</span>
                                                                                <br />
                                                                                <span className="text-xs text-muted-foreground">ISO 10816: A ≤ 1.8, B ≤ 4.5, C ≤ 7.1, D &gt; 7.1</span>
                                                                            </div>
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                )}
                                                            />
                                                        </div>
                                                        {/* Live ISO 10816 zone for Pump Leg 2 */}
                                                        {(() => {
                                                            const velocity = parseFloat(formValues.vibrationData?.pump?.leg2?.velocity || '0');
                                                            if (velocity > 0) {
                                                                const zone = getISO10816Zone(velocity);
                                                                return (
                                                                    <div className="flex flex-col gap-1">
                                                                        <div className="text-xs font-mono">{safeDisplay(velocity, 2)} mm/s</div>
                                                                        <Badge className={`px-2 py-1 rounded text-xs font-bold ${zone.color} w-fit`}>
                                                                            Zone {zone.zone}
                                                                        </Badge>
                                                                    </div>
                                                                );
                                                            }
                                                            return null;
                                                        })()}
                                                    </CardContent>
                                                </Card>
                                                {/* Pump Leg 3 */}
                                                <Card>
                                                    <CardHeader className="pb-3">
                                                        <CardTitle className="flex items-center gap-2 text-sm">
                                                            <Cpu className="h-4 w-4 text-orange-500" />
                                                            Leg 3
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent className="space-y-3">
                                                        <div>
                                                            <Label className="text-xs font-medium text-muted-foreground">Velocity (mm/s)</Label>
                                                            <Controller
                                                                name="vibrationData.pump.leg3.velocity"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Input
                                                                                {...field}
                                                                                value={field.value ?? ''}
                                                                                placeholder="2.2"
                                                                                type="number"
                                                                                step="0.1"
                                                                                className={`mt-1 ${getVibrationInputColor(field.value)}`}
                                                                                onFocus={() => setActivePoint('pump.leg3')}
                                                                                onBlur={() => setActivePoint(null)}
                                                                            />
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            <div className="text-center">
                                                                                <span className="font-semibold">Pump Leg 3 Velocity</span>
                                                                                <br />
                                                                                <span className="text-xs">Enter velocity reading in mm/s</span>
                                                                                <br />
                                                                                <span className="text-xs text-muted-foreground">ISO 10816: A ≤ 1.8, B ≤ 4.5, C ≤ 7.1, D &gt; 7.1</span>
                                                                            </div>
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                )}
                                                            />
                                                        </div>
                                                        {/* Live ISO 10816 zone for Pump Leg 3 */}
                                                        {(() => {
                                                            const velocity = parseFloat(formValues.vibrationData?.pump?.leg3?.velocity || '0');
                                                            if (velocity > 0) {
                                                                const zone = getISO10816Zone(velocity);
                                                                return (
                                                                    <div className="flex flex-col gap-1">
                                                                        <div className="text-xs font-mono">{safeDisplay(velocity, 2)} mm/s</div>
                                                                        <Badge className={`px-2 py-1 rounded text-xs font-bold ${zone.color} w-fit`}>
                                                                            Zone {zone.zone}
                                                                        </Badge>
                                                                    </div>
                                                                );
                                                            }
                                                            return null;
                                                        })()}
                                                    </CardContent>
                                                </Card>

                                                {/* Pump Leg 4 */}
                                                <Card>
                                                    <CardHeader className="pb-3">
                                                        <CardTitle className="flex items-center gap-2 text-sm">
                                                            <Cpu className="h-4 w-4 text-purple-500" />
                                                            Leg 4
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent className="space-y-3">
                                                        <div>
                                                            <Label className="text-xs font-medium text-muted-foreground">Velocity (mm/s)</Label>
                                                            <Controller
                                                                name="vibrationData.pump.leg4.velocity"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Input
                                                                                {...field}
                                                                                value={field.value ?? ''}
                                                                                placeholder="3.1"
                                                                                type="number"
                                                                                step="0.1"
                                                                                className={`mt-1 ${getVibrationInputColor(field.value)}`}
                                                                                onFocus={() => setActivePoint('pump.leg4')}
                                                                                onBlur={() => setActivePoint(null)}
                                                                            />
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            <div className="text-center">
                                                                                <span className="font-semibold">Pump Leg 4 Velocity</span>
                                                                                <br />
                                                                                <span className="text-xs">Enter velocity reading in mm/s</span>
                                                                                <br />
                                                                                <span className="text-xs text-muted-foreground">ISO 10816: A ≤ 1.8, B ≤ 4.5, C ≤ 7.1, D &gt; 7.1</span>
                                                                            </div>
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                )}
                                                            />
                                                        </div>
                                                        {/* Live ISO 10816 zone for Pump Leg 4 */}
                                                        {(() => {
                                                            const velocity = parseFloat(formValues.vibrationData?.pump?.leg4?.velocity || '0');
                                                            if (velocity > 0) {
                                                                const zone = getISO10816Zone(velocity);
                                                                return (
                                                                    <div className="flex flex-col gap-1">
                                                                        <div className="text-xs font-mono">{safeDisplay(velocity, 2)} mm/s</div>
                                                                        <Badge className={`px-2 py-1 rounded text-xs font-bold ${zone.color} w-fit`}>
                                                                            Zone {zone.zone}
                                                                        </Badge>
                                                                    </div>
                                                                );
                                                            }
                                                            return null;
                                                        })()}
                                                    </CardContent>
                                                </Card>
                                            </div>

                                        </div>
                                    </TabsContent>

                                    {/* Motor Measurements */}
                                    <TabsContent value="motor" className="space-y-6">
                                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                            {/* Non-Drive End */}
                                            <Card>
                                                <CardHeader className="pb-3 pt-4 px-4">
                                                    <CardTitle className="flex items-center gap-2">
                                                        <Zap className="h-4 w-4 text-green-500" />
                                                        Motor - Non-Drive End (NDE)
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                    <div className="grid grid-cols-4 gap-3">
                                                        <div>
                                                            <Label className="text-xs font-medium">Bearing Vibration</Label>
                                                            <div className="space-y-2 mt-1">
                                                                {['bv', 'bg'].map((field) => (
                                                                    <Controller
                                                                        key={`motor.nde.${field}`}
                                                                        name={`vibrationData.motor.nde.${field}` as any}
                                                                        control={control}
                                                                        render={({ field: f }) => (
                                                                            <Tooltip>
                                                                                <TooltipTrigger asChild>
                                                                                    <Input
                                                                                        {...f}
                                                                                        value={f.value ?? ''}
                                                                                        placeholder={field.toUpperCase()}
                                                                                        type="number"
                                                                                        className={`text-xs ${getVibrationInputColor(f.value)}`}
                                                                                        onFocus={() => setActivePoint('motor.nde')}
                                                                                        onBlur={() => setActivePoint(null)}
                                                                                    />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    {getVibrationTooltip(field, field === 'bg' ? 'displacement' : 'velocity')}
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        )}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label className="text-xs font-medium">Velocity (mm/s)</Label>
                                                            <div className="space-y-2 mt-1">
                                                                {['velV', 'velH', 'velAxl'].map((field) => (
                                                                    <Controller
                                                                        key={`motor.nde.${field}`}
                                                                        name={`vibrationData.motor.nde.${field}` as any}
                                                                        control={control}
                                                                        render={({ field: f }) => (
                                                                            <Tooltip>
                                                                                <TooltipTrigger asChild>
                                                                                    <Input
                                                                                        {...f}
                                                                                        value={f.value ?? ''}
                                                                                        placeholder={field.toUpperCase()}
                                                                                        type="number"
                                                                                        className={`text-xs ${getVibrationInputColor(f.value)}`}
                                                                                        onFocus={() => setActivePoint('motor.nde')}
                                                                                        onBlur={() => setActivePoint(null)}
                                                                                    />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    {getVibrationTooltip(field, 'velocity')}
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        )}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label className="text-xs font-medium">Acceleration (m/s²)</Label>
                                                            <div className="space-y-2 mt-1">
                                                                {['accV', 'accH', 'accAxl'].map((field) => (
                                                                    <Controller
                                                                        key={`motor.nde.${field}`}
                                                                        name={`vibrationData.motor.nde.${field}` as any}
                                                                        control={control}
                                                                        render={({ field: f }) => (
                                                                            <Tooltip>
                                                                                <TooltipTrigger asChild>
                                                                                    <Input
                                                                                        {...f}
                                                                                        value={f.value ?? ''}
                                                                                        placeholder={field.toUpperCase()}
                                                                                        type="number"
                                                                                        className={`text-xs ${getVibrationInputColor(f.value)}`}
                                                                                    />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    {getVibrationTooltip(field, 'acceleration')}
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        )}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label className="text-xs font-medium">Temperature (°C)</Label>
                                                            <Controller
                                                                name="vibrationData.motor.nde.temp"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Input
                                                                                {...field}
                                                                                placeholder="42"
                                                                                type="number"
                                                                                className={`text-xs ${getVibrationInputColor(field.value)}`}
                                                                            />
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            {getVibrationTooltip('temp', 'temperature')}
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                )}
                                                            />
                                                        </div>
                                                    </div>
                                                    {/* Live RMS and ISO 10816 zone for Motor NDE */}
                                                    {(() => {
                                                        const nde = formValues.vibrationData?.motor?.nde || {};
                                                        const rms = calcRMSVelocity(nde);
                                                        const zone = getISO10816Zone(rms);
                                                        return (
                                                            <div className="flex items-center gap-2 mt-2">
                                                                <span className="font-semibold text-sm">RMS Velocity:</span>
                                                                <span className="font-mono text-sm">{safeDisplay(rms, 2)} mm/s</span>
                                                                <Badge className={`px-2 py-1 rounded text-xs font-bold ${zone.color}`}>
                                                                    Zone {zone.zone} ({zone.description})
                                                                </Badge>
                                                                {zone.zone === 'D' && (
                                                                    <span className="text-red-600 font-semibold ml-2 text-sm">Warning: Unacceptable!</span>
                                                                )}
                                                            </div>
                                                        );
                                                    })()}
                                                </CardContent>
                                            </Card>

                                            {/* Drive End */}
                                            <Card>
                                                <CardHeader className="pb-3 pt-4 px-4">
                                                    <CardTitle className="flex items-center gap-2">
                                                        <Zap className="h-4 w-4 text-primary" />
                                                        Motor - Drive End (DE)
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                    <div className="grid grid-cols-4 gap-3">
                                                        <div>
                                                            <Label className="text-xs font-medium">Bearing Vibration</Label>
                                                            <div className="space-y-2 mt-1">
                                                                {['bv', 'bg'].map((field) => (
                                                                    <Controller
                                                                        key={`motor.de.${field}`}
                                                                        name={`vibrationData.motor.de.${field}` as any}
                                                                        control={control}
                                                                        render={({ field: f }) => (
                                                                            <Tooltip>
                                                                                <TooltipTrigger asChild>
                                                                                    <Input
                                                                                        {...f}
                                                                                        value={f.value ?? ''}
                                                                                        placeholder={field.toUpperCase()}
                                                                                        type="number"
                                                                                        className={`text-xs ${getVibrationInputColor(f.value)}`}
                                                                                        onFocus={() => setActivePoint('motor.de')}
                                                                                        onBlur={() => setActivePoint(null)}
                                                                                    />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    {getVibrationTooltip(field, field === 'bg' ? 'displacement' : 'velocity')}
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        )}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label className="text-xs font-medium">Velocity (mm/s)</Label>
                                                            <div className="space-y-2 mt-1">
                                                                {['velV', 'velH', 'velAxl'].map((field) => (
                                                                    <Controller
                                                                        key={`motor.de.${field}`}
                                                                        name={`vibrationData.motor.de.${field}` as any}
                                                                        control={control}
                                                                        render={({ field: f }) => (
                                                                            <Tooltip>
                                                                                <TooltipTrigger asChild>
                                                                                    <Input
                                                                                        {...f}
                                                                                        value={f.value ?? ''}
                                                                                        placeholder={field.toUpperCase()}
                                                                                        type="number"
                                                                                        className={`text-xs ${getVibrationInputColor(f.value)}`}
                                                                                        onFocus={() => setActivePoint('motor.de')}
                                                                                        onBlur={() => setActivePoint(null)}
                                                                                    />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    {getVibrationTooltip(field, 'velocity')}
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        )}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label className="text-xs font-medium">Acceleration (m/s²)</Label>
                                                            <div className="space-y-2 mt-1">
                                                                {['accV', 'accH', 'accAxl'].map((field) => (
                                                                    <Controller
                                                                        key={`motor.de.${field}`}
                                                                        name={`vibrationData.motor.de.${field}` as any}
                                                                        control={control}
                                                                        render={({ field: f }) => (
                                                                            <Tooltip>
                                                                                <TooltipTrigger asChild>
                                                                                    <Input
                                                                                        {...f}
                                                                                        value={f.value ?? ''}
                                                                                        placeholder={field.toUpperCase()}
                                                                                        type="number"
                                                                                        className={`text-xs ${getVibrationInputColor(f.value)}`}
                                                                                    />
                                                                                </TooltipTrigger>
                                                                                <TooltipContent>
                                                                                    {getVibrationTooltip(field, 'acceleration')}
                                                                                </TooltipContent>
                                                                            </Tooltip>
                                                                        )}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label className="text-xs font-medium">Temperature (°C)</Label>
                                                            <Controller
                                                                name="vibrationData.motor.de.temp"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Input
                                                                                {...field}
                                                                                placeholder="44"
                                                                                type="number"
                                                                                className={`text-xs ${getVibrationInputColor(field.value)}`}
                                                                            />
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            {getVibrationTooltip('temp', 'temperature')}
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                )}
                                                            />
                                                        </div>
                                                    </div>
                                                    {/* Live RMS and ISO 10816 zone for Motor DE */}
                                                    {(() => {
                                                        const de = formValues.vibrationData?.motor?.de || {};
                                                        const rms = calcRMSVelocity(de);
                                                        const zone = getISO10816Zone(rms);
                                                        return (
                                                            <div className="flex items-center gap-2 mt-2">
                                                                <span className="font-semibold text-sm">RMS Velocity:</span>
                                                                <span className="font-mono text-sm">{safeDisplay(rms, 2)} mm/s</span>
                                                                <Badge className={`px-2 py-1 rounded text-xs font-bold ${zone.color}`}>
                                                                    Zone {zone.zone} ({zone.description})
                                                                </Badge>
                                                                {zone.zone === 'D' && (
                                                                    <span className="text-red-600 font-semibold ml-2 text-sm">Warning: Unacceptable!</span>
                                                                )}
                                                            </div>
                                                        );
                                                    })()}
                                                </CardContent>
                                            </Card>
                                        </div>

                                        {/* Motor Legs 1-4 Sections - Simplified */}
                                        <div className="space-y-6">
                                            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                                                <Zap className="h-5 w-5 text-green-500" />
                                                Motor Leg Measurements (Simplified)
                                            </h3>

                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                                {/* Motor Leg 1 */}
                                                <Card>
                                                    <CardHeader className="pb-3">
                                                        <CardTitle className="flex items-center gap-2 text-sm">
                                                            <Zap className="h-4 w-4 text-blue-500" />
                                                            Leg 1
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent className="space-y-3">
                                                        <div>
                                                            <Label className="text-xs font-medium text-muted-foreground">Velocity (mm/s)</Label>
                                                            <Controller
                                                                name="vibrationData.motor.leg1.velocity"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Input
                                                                                {...field}
                                                                                value={field.value ?? ''}
                                                                                placeholder="1.8"
                                                                                type="number"
                                                                                step="0.1"
                                                                                className={`mt-1 ${getVibrationInputColor(field.value)}`}
                                                                                onFocus={() => setActivePoint('motor.leg1')}
                                                                                onBlur={() => setActivePoint(null)}
                                                                            />
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            <div className="text-center">
                                                                                <span className="font-semibold">Motor Leg 1 Velocity</span>
                                                                                <br />
                                                                                <span className="text-xs">Enter velocity reading in mm/s</span>
                                                                                <br />
                                                                                <span className="text-xs text-muted-foreground">ISO 10816: A ≤ 1.8, B ≤ 4.5, C ≤ 7.1, D &gt; 7.1</span>
                                                                            </div>
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                )}
                                                            />
                                                        </div>
                                                        {/* Live ISO 10816 zone for Motor Leg 1 */}
                                                        {(() => {
                                                            const velocity = parseFloat(formValues.vibrationData?.motor?.leg1?.velocity || '0');
                                                            if (velocity > 0) {
                                                                const zone = getISO10816Zone(velocity);
                                                                return (
                                                                    <div className="flex flex-col gap-1">
                                                                        <div className="text-xs font-mono">{safeDisplay(velocity, 2)} mm/s</div>
                                                                        <Badge className={`px-2 py-1 rounded text-xs font-bold ${zone.color} w-fit`}>
                                                                            Zone {zone.zone}
                                                                        </Badge>
                                                                    </div>
                                                                );
                                                            }
                                                            return null;
                                                        })()}
                                                    </CardContent>
                                                </Card>

                                                {/* Motor Leg 2 */}
                                                <Card>
                                                    <CardHeader className="pb-3">
                                                        <CardTitle className="flex items-center gap-2 text-sm">
                                                            <Zap className="h-4 w-4 text-green-500" />
                                                            Leg 2
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent className="space-y-3">
                                                        <div>
                                                            <Label className="text-xs font-medium text-muted-foreground">Velocity (mm/s)</Label>
                                                            <Controller
                                                                name="vibrationData.motor.leg2.velocity"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Input
                                                                                {...field}
                                                                                value={field.value ?? ''}
                                                                                placeholder="2.1"
                                                                                type="number"
                                                                                step="0.1"
                                                                                className={`mt-1 ${getVibrationInputColor(field.value)}`}
                                                                                onFocus={() => setActivePoint('motor.leg2')}
                                                                                onBlur={() => setActivePoint(null)}
                                                                            />
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            <div className="text-center">
                                                                                <span className="font-semibold">Motor Leg 2 Velocity</span>
                                                                                <br />
                                                                                <span className="text-xs">Enter velocity reading in mm/s</span>
                                                                                <br />
                                                                                <span className="text-xs text-muted-foreground">ISO 10816: A ≤ 1.8, B ≤ 4.5, C ≤ 7.1, D &gt; 7.1</span>
                                                                            </div>
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                )}
                                                            />
                                                        </div>
                                                        {/* Live ISO 10816 zone for Motor Leg 2 */}
                                                        {(() => {
                                                            const velocity = parseFloat(formValues.vibrationData?.motor?.leg2?.velocity || '0');
                                                            if (velocity > 0) {
                                                                const zone = getISO10816Zone(velocity);
                                                                return (
                                                                    <div className="flex flex-col gap-1">
                                                                        <div className="text-xs font-mono">{safeDisplay(velocity, 2)} mm/s</div>
                                                                        <Badge className={`px-2 py-1 rounded text-xs font-bold ${zone.color} w-fit`}>
                                                                            Zone {zone.zone}
                                                                        </Badge>
                                                                    </div>
                                                                );
                                                            }
                                                            return null;
                                                        })()}
                                                    </CardContent>
                                                </Card>

                                                {/* Motor Leg 3 */}
                                                <Card>
                                                    <CardHeader className="pb-3">
                                                        <CardTitle className="flex items-center gap-2 text-sm">
                                                            <Zap className="h-4 w-4 text-orange-500" />
                                                            Leg 3
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent className="space-y-3">
                                                        <div>
                                                            <Label className="text-xs font-medium text-muted-foreground">Velocity (mm/s)</Label>
                                                            <Controller
                                                                name="vibrationData.motor.leg3.velocity"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Input
                                                                                {...field}
                                                                                value={field.value ?? ''}
                                                                                placeholder="1.9"
                                                                                type="number"
                                                                                step="0.1"
                                                                                className={`mt-1 ${getVibrationInputColor(field.value)}`}
                                                                                onFocus={() => setActivePoint('motor.leg3')}
                                                                                onBlur={() => setActivePoint(null)}
                                                                            />
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            <div className="text-center">
                                                                                <span className="font-semibold">Motor Leg 3 Velocity</span>
                                                                                <br />
                                                                                <span className="text-xs">Enter velocity reading in mm/s</span>
                                                                                <br />
                                                                                <span className="text-xs text-muted-foreground">ISO 10816: A ≤ 1.8, B ≤ 4.5, C ≤ 7.1, D &gt; 7.1</span>
                                                                            </div>
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                )}
                                                            />
                                                        </div>
                                                        {/* Live ISO 10816 zone for Motor Leg 3 */}
                                                        {(() => {
                                                            const velocity = parseFloat(formValues.vibrationData?.motor?.leg3?.velocity || '0');
                                                            if (velocity > 0) {
                                                                const zone = getISO10816Zone(velocity);
                                                                return (
                                                                    <div className="flex flex-col gap-1">
                                                                        <div className="text-xs font-mono">{safeDisplay(velocity, 2)} mm/s</div>
                                                                        <Badge className={`px-2 py-1 rounded text-xs font-bold ${zone.color} w-fit`}>
                                                                            Zone {zone.zone}
                                                                        </Badge>
                                                                    </div>
                                                                );
                                                            }
                                                            return null;
                                                        })()}
                                                    </CardContent>
                                                </Card>

                                                {/* Motor Leg 4 */}
                                                <Card>
                                                    <CardHeader className="pb-3">
                                                        <CardTitle className="flex items-center gap-2 text-sm">
                                                            <Zap className="h-4 w-4 text-purple-500" />
                                                            Leg 4
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent className="space-y-3">
                                                        <div>
                                                            <Label className="text-xs font-medium text-muted-foreground">Velocity (mm/s)</Label>
                                                            <Controller
                                                                name="vibrationData.motor.leg4.velocity"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Input
                                                                                {...field}
                                                                                value={field.value ?? ''}
                                                                                placeholder="2.3"
                                                                                type="number"
                                                                                step="0.1"
                                                                                className={`mt-1 ${getVibrationInputColor(field.value)}`}
                                                                                onFocus={() => setActivePoint('motor.leg4')}
                                                                                onBlur={() => setActivePoint(null)}
                                                                            />
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            <div className="text-center">
                                                                                <span className="font-semibold">Motor Leg 4 Velocity</span>
                                                                                <br />
                                                                                <span className="text-xs">Enter velocity reading in mm/s</span>
                                                                                <br />
                                                                                <span className="text-xs text-muted-foreground">ISO 10816: A ≤ 1.8, B ≤ 4.5, C ≤ 7.1, D &gt; 7.1</span>
                                                                            </div>
                                                                        </TooltipContent>
                                                                    </Tooltip>
                                                                )}
                                                            />
                                                        </div>
                                                        {/* Live ISO 10816 zone for Motor Leg 4 */}
                                                        {(() => {
                                                            const velocity = parseFloat(formValues.vibrationData?.motor?.leg4?.velocity || '0');
                                                            if (velocity > 0) {
                                                                const zone = getISO10816Zone(velocity);
                                                                return (
                                                                    <div className="flex flex-col gap-1">
                                                                        <div className="text-xs font-mono">{safeDisplay(velocity, 2)} mm/s</div>
                                                                        <Badge className={`px-2 py-1 rounded text-xs font-bold ${zone.color} w-fit`}>
                                                                            Zone {zone.zone}
                                                                        </Badge>
                                                                    </div>
                                                                );
                                                            }
                                                            return null;
                                                        })()}
                                                    </CardContent>
                                                </Card>


                                            </div>
                                        </div>
                                    </TabsContent>


                                </Tabs>

                            </div>
                        )}

                        {/* Step 4: Comprehensive Analysis & Review */}
                        {
                            currentStep === 3 && (
                                <div className="p-6 space-y-6">
                                    {(() => {
                                        // Extract vibration data from form for analysis
                                        const formData = getValues();
                                        const vibrationData = formData.vibrationData;

                                        // Check if we have valid vibration data before showing analytics
                                        const hasValidData = hasValidVibrationData(vibrationData);

                                        // Use master health score from failure analysis engine
                                        const integratedHealthScore = masterHealth.overallHealthScore;

                                        const enhancedMasterHealth = {
                                            ...masterHealth,
                                            overallHealthScore: integratedHealthScore,
                                            recommendations: [
                                                ...masterHealth.recommendations
                                            ]
                                        };

                                        // If no valid vibration data, show placeholder messages
                                        if (!hasValidData) {
                                            return (
                                                <div className="space-y-6">
                                                    {/* Main Placeholder Card */}
                                                    <Card
                                                        className="border-border dark:border-border"
                                                        style={{ background: 'hsl(var(--card))' }}
                                                    >
                                                        <CardHeader className="text-center pb-3 pt-4 px-4">
                                                            <CardTitle className="flex flex-col items-center gap-4">
                                                                <div className="p-4 bg-primary rounded-full shadow-lg">
                                                                    <BarChart className="h-8 w-8 text-primary-foreground" />
                                                                </div>
                                                                <div>
                                                                    <h3 className="text-2xl font-bold text-foreground">Analytics Ready</h3>
                                                                    <p className="text-muted-foreground mt-2">Complete vibration measurements to generate comprehensive analysis</p>
                                                                </div>
                                                            </CardTitle>
                                                        </CardHeader>
                                                        <CardContent className="space-y-6">
                                                            <div className="text-center">
                                                                <p className="text-lg text-foreground mb-4">
                                                                    Enter vibration measurements in <strong>Step 3</strong> to unlock:
                                                                </p>
                                                            </div>

                                                            {/* Feature Preview Grid */}
                                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                                                <div className="p-4 bg-card rounded-lg shadow-sm border-l-4 border-primary/60">
                                                                    <div className="flex items-center gap-3 mb-2">
                                                                        <Brain className="h-5 w-5 text-primary" />
                                                                        <span className="font-semibold text-foreground">Unified AI Assessment</span>
                                                                    </div>
                                                                    <p className="text-sm text-muted-foreground">
                                                                        Complete AI-powered analysis center with all assessment features in one location
                                                                    </p>
                                                                </div>

                                                                <div className="p-4 bg-card rounded-lg shadow-sm border-l-4 border-destructive/60">
                                                                    <div className="flex items-center gap-3 mb-2">
                                                                        <AlertTriangle className="h-5 w-5 text-destructive" />
                                                                        <span className="font-semibold text-foreground">Failure Analysis</span>
                                                                    </div>
                                                                    <p className="text-sm text-muted-foreground">
                                                                        Comprehensive analysis of 17+ failure modes including unbalance, misalignment, and bearing defects
                                                                    </p>
                                                                </div>

                                                                <div className="p-4 bg-card rounded-lg shadow-sm border-l-4 border-emerald-500/60">
                                                                    <div className="flex items-center gap-3 mb-2">
                                                                        <TrendingUp className="h-5 w-5 text-emerald-500" />
                                                                        <span className="font-semibold text-foreground">Health Dashboard</span>
                                                                    </div>
                                                                    <p className="text-sm text-muted-foreground">
                                                                        Master health assessment with combined scoring and maintenance recommendations
                                                                    </p>
                                                                </div>

                                                                <div className="p-4 bg-card rounded-lg shadow-sm border-l-4 border-amber-500/60">
                                                                    <div className="flex items-center gap-3 mb-2">
                                                                        <BarChart className="h-5 w-5 text-amber-500" />
                                                                        <span className="font-semibold text-foreground">Reliability Metrics</span>
                                                                    </div>
                                                                    <p className="text-sm text-muted-foreground">
                                                                        MTBF, RUL predictions, Weibull analysis, and ISO compliance metrics
                                                                    </p>
                                                                </div>
                                                            </div>

                                                            {/* Instructions */}
                                                            <div className="bg-muted/50 dark:bg-muted/30 rounded-lg p-4 border border-border">
                                                                <h4 className="font-semibold text-foreground mb-2 flex items-center gap-2">
                                                                    <Info className="h-4 w-4 text-primary" />
                                                                    How to Generate Analytics
                                                                </h4>
                                                                <ol className="text-sm text-muted-foreground space-y-1">
                                                                    <li>1. Navigate back to <strong>Step 3: Vibration Measurements</strong></li>
                                                                    <li>2. Enter vibration readings for pump and/or motor equipment</li>
                                                                    <li>3. Include at least one non-zero measurement (NDE/DE or Leg readings)</li>
                                                                    <li>4. Return to this step to view comprehensive analytics</li>
                                                                </ol>
                                                            </div>

                                                            {/* Quick Navigation */}
                                                            <div className="text-center">
                                                                <Button
                                                                    type="button"
                                                                    onClick={() => setCurrentStep(2)}
                                                                    className="bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-2"
                                                                >
                                                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                                                    Go to Step 3: Vibration Measurements
                                                                </Button>
                                                            </div>
                                                        </CardContent>
                                                    </Card>
                                                </div>
                                            );
                                        }

                                        return (
                                            <>
                                                {/* All AI components consolidated into unified AI Assessment Center below */}

                                                {/* Enhanced Failure Analysis Carousel */}
                                                <EnhancedFailureAnalysisCarousel
                                                    analyses={combinedAnalyses}
                                                    autoRotationInterval={6000}
                                                    showControls={true}
                                                    className="mb-4"
                                                />

                                                {/* Statistical Analysis Dashboard */}
                                                <Card
                                                    className="border-border dark:border-border"
                                                    style={{ background: 'hsl(var(--card))' }}
                                                >
                                                    <CardHeader className="pb-3 pt-4 px-4">
                                                        <CardTitle className="flex items-center gap-3">
                                                            <div className="p-2 bg-emerald-500 rounded-lg">
                                                                <TrendingUp className="h-5 w-5 text-white" />
                                                            </div>
                                                            <div>
                                                                <h3 className="text-lg font-bold text-foreground">Statistical Analysis Dashboard</h3>
                                                                <p className="text-sm text-muted-foreground">Advanced statistical metrics and reliability indicators</p>
                                                            </div>
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent className="space-y-6">
                                                        {reliabilityData ? (
                                                            <>
                                                                {/* Key Reliability Metrics */}
                                                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                                                    <div className="text-center p-4 bg-card rounded-lg shadow-sm border border-border">
                                                                        <div className="text-2xl font-bold text-primary">
                                                                            {safeDisplay(reliabilityData.reliability_metrics.mtbf, 0)}h
                                                                        </div>
                                                                        <div className="text-xs text-primary/80">MTBF</div>
                                                                        <div className="text-xs text-muted-foreground">Mean Time Between Failures</div>
                                                                    </div>
                                                                    <div className="text-center p-4 bg-card rounded-lg shadow-sm border border-border">
                                                                        <div className="text-2xl font-bold text-emerald-600">
                                                                            {safeDisplay(reliabilityData.reliability_metrics.availability, 1)}%
                                                                        </div>
                                                                        <div className="text-xs text-emerald-600">Availability</div>
                                                                        <div className="text-xs text-muted-foreground">System Uptime</div>
                                                                    </div>
                                                                    <div className="text-center p-4 bg-card rounded-lg shadow-sm border border-border">
                                                                        <div className="text-2xl font-bold text-violet-600">
                                                                            {safeDisplay(reliabilityData.rul_prediction.remaining_useful_life, 0)}h
                                                                        </div>
                                                                        <div className="text-xs text-violet-600">RUL</div>
                                                                        <div className="text-xs text-muted-foreground">Remaining Useful Life</div>
                                                                    </div>
                                                                    <div className="text-center p-4 bg-card rounded-lg shadow-sm border border-border">
                                                                        <div className="text-2xl font-bold text-amber-600">
                                                                            {safeDisplay(reliabilityData.weibull_analysis.beta, 2)}
                                                                        </div>
                                                                        <div className="text-xs text-amber-600">β (Beta)</div>
                                                                        <div className="text-xs text-muted-foreground">Weibull Shape</div>
                                                                    </div>
                                                                </div>

                                                                {/* Failure Mode Analysis */}
                                                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                                                    <div>
                                                                        <h4 className="font-semibold text-foreground mb-3 flex items-center gap-2">
                                                                            <AlertTriangle className="h-4 w-4 text-destructive" />
                                                                            Failure Mode Analysis
                                                                        </h4>
                                                                        <div className="space-y-2">
                                                                            {reliabilityData.failure_modes.map((mode: any, index: number) => (
                                                                                <div key={index} className="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
                                                                                    <div>
                                                                                        <div className="font-medium text-sm text-foreground">{mode.mode}</div>
                                                                                        <div className="text-xs text-muted-foreground">RPN: {safeDisplay(mode.rpn, 0)}</div>
                                                                                    </div>
                                                                                    <div className="text-right">
                                                                                        <div className="text-sm font-bold text-destructive">{safeDisplay(mode.probability * 100, 1)}%</div>
                                                                                        <div className="text-xs text-muted-foreground">Probability</div>
                                                                                    </div>
                                                                                </div>
                                                                            ))}
                                                                        </div>
                                                                    </div>

                                                                    <div>
                                                                        <h4 className="font-semibold text-foreground mb-3 flex items-center gap-2">
                                                                            <Target className="h-4 w-4 text-primary" />
                                                                            Maintenance Optimization
                                                                        </h4>
                                                                        <div className="space-y-3">
                                                                            <div className="p-3 bg-card rounded-lg border border-border">
                                                                                <div className="flex items-center justify-between mb-2">
                                                                                    <span className="text-sm font-medium text-foreground">Optimal Interval</span>
                                                                                    <span className="text-sm font-bold text-primary">{safeDisplay(reliabilityData.maintenance_optimization.optimal_interval, 0, 'N/A', 'optimal interval')}h</span>
                                                                                </div>
                                                                                <div className="text-xs text-muted-foreground">Recommended maintenance frequency</div>
                                                                            </div>
                                                                            <div className="p-3 bg-card rounded-lg border border-border">
                                                                                <div className="flex items-center justify-between mb-2">
                                                                                    <span className="text-sm font-medium text-foreground">Cost Savings</span>
                                                                                    <span className="text-sm font-bold text-emerald-600">${reliabilityData.maintenance_optimization.cost_savings.toLocaleString()}</span>
                                                                                </div>
                                                                                <div className="text-xs text-muted-foreground">Annual projected savings</div>
                                                                            </div>
                                                                            <div className="p-3 bg-card rounded-lg border border-border">
                                                                                <div className="text-sm font-medium mb-2 text-foreground">Recommended Actions</div>
                                                                                <ul className="text-xs text-muted-foreground space-y-1">
                                                                                    {reliabilityData.maintenance_optimization.recommended_actions.map((action: string, index: number) => (
                                                                                        <li key={index} className="flex items-start gap-1">
                                                                                            <span className="text-emerald-500 mt-0.5">•</span>
                                                                                            <span>{action}</span>
                                                                                        </li>
                                                                                    ))}
                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </>
                                                        ) : (
                                                            <div className="text-center py-8">
                                                                <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                                                                <p className="text-muted-foreground mb-2">Statistical Analysis Ready</p>
                                                                <p className="text-sm text-muted-foreground">
                                                                    Enter vibration measurements to generate comprehensive statistical analysis
                                                                </p>
                                                            </div>
                                                        )}
                                                    </CardContent>
                                                </Card>
                                            </>
                                        );
                                    })()}






































































                                </div >
                            )
                        }
                    </form >
                </div >

                {/* Footer with Navigation */}
                <div className="border-t bg-muted/30 px-6 py-2">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            {currentStep > 0 && (
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={prevStep}
                                    className="flex items-center gap-2"
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                    Previous
                                </Button>
                            )}
                        </div>

                        <div className="flex items-center gap-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={onClose}
                            >
                                Cancel
                            </Button>

                            {currentStep < FORM_STEPS.length - 1 ? (
                                <Button
                                    type="button"
                                    onClick={nextStep}
                                    disabled={currentStep === 0 && selectedEquipment.length === 0}
                                    className="flex items-center gap-2"
                                >
                                    Next
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            ) : (
                                <Button
                                    type="submit"
                                    onClick={handleSubmit(onSubmit)}
                                    className="flex items-center gap-2"
                                >
                                    <CheckCircle className="h-4 w-4" />
                                    Save Vibration Data
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
            </DialogContent>

            {/* Alert Dialog */}
            <AlertDialog open={showAlert} onOpenChange={setShowAlert}>
                <AlertDialogContent className="sm:max-w-[500px] p-6 rounded-lg">
                    <AlertDialogHeader>
                        <AlertDialogTitle className="text-3xl font-extrabold mb-2 text-center">
                            {alertType === 'error' ? 'Submission Failed' : 'Submission Successful!'}
                        </AlertDialogTitle>
                        <AlertDialogDescription className="text-lg text-center">
                            {alertType === 'error' ? (
                                <span className="text-red-600 font-semibold block text-xl">
                                    Please ensure all required fields are filled. Required fields are marked with a red asterisk (*).
                                </span>
                            ) : (
                                <span className="text-green-600 font-semibold block text-xl">
                                    Comprehensive vibration data for <strong>{alertDetails.pumpNo || 'Multiple Equipment'}</strong> has been recorded on <strong>{alertDetails.date}</strong>.
                                    <br />
                                    All analytics have been updated. Check the Trends tab for detailed insights.
                                </span>
                            )}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter className="flex justify-center mt-2">
                        <AlertDialogAction
                            className="text-xl px-8 py-3 rounded-lg"
                            onClick={() => {
                                setShowAlert(false);
                                if (alertType === 'success') {
                                    onClose();
                                }
                            }}
                        >
                            Understood
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </Dialog>
    );
};

export default EnhancedVibrationForm;
